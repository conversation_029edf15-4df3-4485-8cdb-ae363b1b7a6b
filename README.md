# SW Integration System Documentation

This repository contains comprehensive documentation and templates for building integration systems based on the sw-integration-b365 architecture analysis.

## 📋 Documentation Overview

### 1. [SW Integration Architecture Guide](./SW_INTEGRATION_ARCHITECTURE_GUIDE.md)
**Comprehensive architectural documentation covering:**
- Multi-service architecture overview
- Project structure and organization
- Core components and patterns
- Implementation patterns
- Configuration management
- Error handling strategies
- Testing approaches
- Database integration
- HTTP communication patterns
- Deployment and scaling considerations

### 2. [SW Integration Implementation Templates](./SW_INTEGRATION_IMPLEMENTATION_TEMPLATES.md)
**Ready-to-use code templates including:**
- Project setup (package.json, tsconfig.json, nest-cli.json)
- Main service entry points (Wallet, Launcher, Operator services)
- Configuration templates
- Entity definitions (Integration and Operator entities)
- HTTP handler templates (Balance, Bet, Win handlers)

### 3. [SW Integration Service Templates](./SW_INTEGRATION_SERVICE_TEMPLATES.md)
**Service layer implementation templates:**
- Payment Service with business logic
- Round Service for game round management
- Launcher Service for game URL generation
- Module configurations (Wallet, Launcher, Operator, Database)
- Controller templates
- Utility functions and helpers
- Error mapping implementations

## 🏗️ Architecture Summary

The sw-integration-b365 system follows a **microservices architecture** with these key characteristics:

### Core Services
- **Wallet Service** - Handles payments, balance management, and financial transactions
- **Launcher Service** - Manages game URL generation and game launching
- **Operator Service** - Handles operator-specific integrations and communications
- **Mock Service** - Provides testing and development mock implementations

### Key Patterns
- **HTTP Handler Pattern** - Encapsulates request/response logic for external API calls
- **Service Layer Pattern** - Contains business logic and orchestrates handlers
- **Repository Pattern** - Abstracts database operations
- **Error Mapping Pattern** - Systematic mapping between operator and system errors
- **Configuration-Driven Design** - Environment-based configuration management

### Technology Stack
- **Framework**: NestJS with TypeScript
- **Database**: PostgreSQL with TypeORM
- **HTTP Client**: Superagent
- **Testing**: Mocha with TypeScript decorators
- **Validation**: class-validator
- **Logging**: Structured logging with @skywind-group/sw-utils

## 🚀 Quick Start Guide

### 1. Project Setup
```bash
# Create new project directory
mkdir sw-integration-[operator-name]
cd sw-integration-[operator-name]

# Initialize npm project
npm init -y

# Copy package.json template from implementation guide
# Install dependencies
npm install
```

### 2. Project Structure
```
src/
├── bootstrap/           # Application bootstrapping
├── config.ts           # Configuration management
├── db/                 # Database configuration
├── entities/           # Data models and DTOs
├── errors/             # Custom error definitions
├── launcher/           # Game launcher service
├── operator/           # Operator integration service
├── utils/              # Shared utilities
├── wallet/             # Wallet service
├── mainWallet.ts       # Wallet service entry point
├── mainLauncher.ts     # Launcher service entry point
└── mainOperator.ts     # Operator service entry point
```

### 3. Implementation Steps

1. **Configure Environment**
   - Copy configuration template from implementation guide
   - Set up environment variables
   - Configure database connection

2. **Implement Entities**
   - Define operator-specific entities
   - Create integration entities
   - Set up validation rules

3. **Create HTTP Handlers**
   - Implement base HTTP handler
   - Create specific handlers (Balance, Bet, Win)
   - Add error mapping

4. **Build Services**
   - Implement Payment Service
   - Create Round Service
   - Build Launcher Service

5. **Set Up Modules**
   - Configure Wallet Module
   - Set up Launcher Module
   - Create Operator Module

6. **Add Testing**
   - Create unit tests
   - Set up mock services
   - Implement integration tests

## 🔧 Configuration

### Environment Variables
```bash
# Server Configuration
SERVER_WALLET_PORT=3000
SERVER_LAUNCHER_PORT=3001
SERVER_OPERATOR_PORT=3002

# Database Configuration
PGDATABASE=management
PGUSER=postgres
PGPASSWORD=password
PGHOST=localhost
PGPORT=5432

# Operator API Configuration
OPERATOR_HTTP_URL=https://api.operator.com
OPERATOR_HTTP_TIMEOUT=5000
OPERATOR_API_VERSION=1.0

# Logging Configuration
LOG_LEVEL=info
LOGGING_OUTPUT_TYPE=console
```

### Build Scripts
```json
{
  "scripts": {
    "build": "npm run version && nest build",
    "start:wallet": "nest start wallet",
    "start:launcher": "nest start launcher",
    "start:operator": "nest start operator",
    "start:mock": "nest start mock",
    "test": "mocha -r ts-node/register --exit src/**/*.spec.ts"
  }
}
```

## 🧪 Testing

### Unit Testing
```typescript
@suite
class PaymentServiceSpec {
    @test
    public async "should process bet payment successfully"() {
        // Arrange
        const request = createMockPaymentRequest();
        
        // Act
        const result = await this.paymentService.commitBetPayment(request);
        
        // Assert
        expect(result).to.deep.equal(expectedBalance);
    }
}
```

### Mock Services
The system includes comprehensive mock implementations for:
- Operator API responses
- Database operations
- HTTP requests
- Error scenarios

## 📦 Deployment

### Docker Support
```dockerfile
FROM node:16-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY lib/ ./lib/
EXPOSE 3000 3001 3002 4054
CMD ["node", "lib/mainWallet.js"]
```

### Health Checks
Each service provides health check endpoints at `/health` for monitoring and load balancing.

## 🔍 Key Features

- **Modular Architecture** - Independent, deployable services
- **Type Safety** - Full TypeScript implementation with strict typing
- **Error Resilience** - Comprehensive error handling and retry mechanisms
- **Configuration Management** - Environment-based configuration
- **Testing Support** - Extensive testing framework with mocks
- **Monitoring** - Built-in logging and metrics
- **Scalability** - Designed for horizontal scaling

## 📚 Additional Resources

- **NestJS Documentation**: https://docs.nestjs.com/
- **TypeORM Documentation**: https://typeorm.io/
- **Class Validator**: https://github.com/typestack/class-validator
- **Mocha Testing**: https://mochajs.org/

## 🤝 Contributing

When implementing a new integration:

1. Follow the established patterns and conventions
2. Use the provided templates as starting points
3. Implement comprehensive tests
4. Document any operator-specific requirements
5. Follow the implementation checklist in the architecture guide

## 📄 License

This documentation is provided for internal use and follows the same licensing as the original sw-integration-b365 codebase.

---

**Note**: This documentation is based on the analysis of the sw-integration-b365 codebase and provides a comprehensive foundation for building similar integration systems. Always refer to the latest operator API documentation and requirements when implementing specific integrations.
