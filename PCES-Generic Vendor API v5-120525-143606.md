Generic Vendor API v5 

Changelog 

DEPRECATED 

The following endpoints were deprecated as all promotional wins are now integrated in the /promo endpoint. Calling these endpoints from this version on will return an error "UNSUPPORTED\_API\_VERSION": 

/freespin (with freespin win) (freespin given from game provider backoffice) 

/freespin (with freespin win) (freespin given via our API) 

/jackpot 

ADDED 

/promo endpoint: 

generic promotion endpoint used for all promotional wins including: 

freespin 

freespin given from game provider backoffice 

freespin given via our API 

jackpot win 

reward win 

tournament win 

cashDrop win 

cashBack win 

redEnvelope win 

Game start 

Open game 

Query parameters 

Example 

Open DEMO game 

Query parameters 

Example 

Open game recap: 

Token 

Freespins 

Freespin API 

Mandatory parameters: 

Optional parameters: 

Freespin cancelation: 

Wallet 

General 

Balance vs Bonus Balance 

Intro 

Data needed to send requests  
Postman 

Legend Of Types in JSON 

Request Fields 

Response Fields 

Calculating Hash 

Example: 

Steps 

JS Example code 

URL 

Headers 

Methods 

/auth 

/debit-credit 

/debit 

/credit 

/rollback 

/promo 

Status Codes And Errors 

Example 

List of all Status Codes 

Round Details 

Query parameters 

Example 

Response 

JSON schema 

JSON schema 

Example raw JSON response 

JSON round details 

Example image response 

JSON with image round details 

Example HTML response 

JSON with image round details 

Status Codes And Errors 

Example 

JSON error message 

List of all Status Codes 

Attachments 

Game start 

Open game 

Our system displays games inside iframe tag. 

So we will pass the URL you provide us with query parameters listed below to this iframe. 

We will always send all parameters listed here even if you don't use all of them on your side. 

Query parameters

| name  | type  | description  | example |
| ----- | :---: | :---- | :---- |
| currency  | String  | Currency code provided in ISO 4217 format  | USD |

| customer  | String  | Our customer identifier  | 2019045569026 |
| ----- | ----- | ----- | :---- |
| demo  | boolea n | If game should open in demo mode  | false |
| gameId  | String  | Providers game identifier (max 50 chars)  | test\_game |
| lang  | String  | Language code in ISO 6391 (default=en)  | en |
| lobby  | String  | Url to redirect customer back to game list (url encoded) | http://casinoapi.sporting  dev.com/apiclient/gameslistsearch |
| platform  | String  | customers device (desktop/mobile)  (d/m)  | d |
| tableId  | String  | Additional providers game data (max 50 chars)  | v1 |
| token  | String  | Token used to verify wallet calls  | 4DD8621822DE6 |
| trader  | String  | Code used for brand/trader/platform separation  | ST499 |
| country  | String  | Customer's country code  | ISO 31661 alpha2 Slovenia: SI) |

Example 

\<providers-url\>? 

currency=USD\&customer=2019045569026\&demo=false\&gameId=test\_game\&lang=en\&lobby=http://casinoapi.sporting dev.com/apiclient/gameslistsearch\&platform=d\&tableId=null\&token=4DD8621822DE66A39627A31F7DA0D03CB45643169D DB4735988550C5CC660B58209A20BCED1A8716\&trader=ST499 

Open DEMO game 

Demo/free play is a game that is played without real money and should not make any calls to wallet API. It is preferred that demo is on a separate URL since the parameter list is different. 

Query parameters

| name  | type  | description  | example |
| :---- | :---- | ----- | :---- |
| currency  | String  | Always FUN for demo mode  | FUN |
| demo  | boolean  | If game should open in demo mode | true |
| gameId  | String  | Providers game identifier  | test\_game |
| lang  | String  | Language code in ISO 6391 (default=en) | en |
| lobby  | String  | Url to redirect customer back to game list (url encoded) | http://casinoapi.sporting dev.com/apiclient/gameslists earch |
| platform  | String  | customers device  (desktop/mobile)  (d/m) | d |

| tableId  | String  | Additional providers game data | v1 |
| :---- | :---- | :---- | :---- |
| trader  | String  | Code used for  brand/trader/platform  separation | ST499 |

Example 

\<providers-demo-url\>?currency=FUN\&demo=true\&gameId=test\_game\&lang=en\&lobby=http://casinoapi.sporting dev.com/apiclient/gameslistsearch\&platform=d\&tableId=null\&trader=ST499 

Open game recap: 

Provider needs to send us list of games with their gameIds and optional tableIds and two urls, one for real play and one for demo game. Both urls should accept the query parameters listed in above tables and open the game. 

Token 

Session token is created and passed as query parameter on open game. 

Bets (/debit request) can be placed with this token for 5 minutes after the token was created. 

With each processed /debit request, the token is refreshed for another 5 minutes. 

Settle bet (/credit request) and /rollback requests are accepted even after this period, but should be received ASAP. Note: the 5 minute period can be extended. 

Freespins 

Our system differentiates 2 different types of freespins/freerounds. First type being given inside game providers backoffice and the second type being given in our backoffice and communicated to game providers system via freespin api. Example of credit requests for both types of freespins can be found below under "/promo". 

When freespin is given in game providers backoffice and sent to our system via credit call, that transaction is marked differently and the value from "freespinRef" field is stored. 

When freespin is given from our backoffice, it is sent to game providers side to the endpoint prepared according to our specification. In that case "freespin" json inside the credit request should include "requested":true and the "freespinRef" should match the freespin reference that was sent to the "give freespin endpoint". 

Freespin API 

This is an optional feature that can be implemented. Since giving freespins encourages players to play more, it is recommended to integrate this part also. 

Game provider should create a POST endpoint that receives below parameters in the json body: 

Mandatory parameters:

| Field  | Type  | Description |
| :---- | :---- | :---- |
| customers  | JsonArray  | List of customers that will receive freespins |

| games  | JsonArray  | List of games where freespins will be available |
| :---- | :---- | ----- |
| numberOfFreespins  | Integer  | Number of freespins per each customer |
| freespinRef  | String  | Reference that needs to be passed to /credit when freespin is used |
| wagerRequirement  | Double  | Minimal bet amount that triggers the freespin |
| validFrom  | Date  | From which date can the freespins be used by customer |
| validUntil  | Date  | Untill which date can the freespins be used by customer |

Optional parameters: 

| Field  | Type  | Description |
| :---- | :---- | :---- |
| maxWin  | Double  | Maximum amount of money that can be won with this freespin reference |
| costPerBet  | Double  | Optional parameter to calculate bet amount |
| coinValueLevel  | Double  | Optional parameter to calculate bet amount |
| lines  | Integer  | Optional parameter to calculate bet amount |
| coins  | Double  | Optional parameter to calculate bet amount |
| denomination  | Double  | Optional parameter to calculate bet amount |
| betPerLine  | Double  | Optional parameter to calculate bet amount |

Optional parameters and endpoint url need to be communicated from game provider to our team so our backoffice and API can be adjusted accordingly. 

Freespin cancelation: 

If game provider supports cancellation of freespins, a DELETE endpoint should be created with freespinRef as path parameter. Example (for cancellation of all freespins given under freespinRef="3e7d7ac3"): 

\<providers-cancel-freespin-url\>/3e7d7ac3/ 

Wallet  
General 

All rounds have to be closed by the client(game provider), we have no fixed expiration time for rounds. For every /debit there must be a /credit request. 

There are two ways to use the API. And different games can use each of the two. Depending on the nature of the game. 

. Using debit and credit in same method (preferred) 

This method should be used when the result of the bet is known at the time of the bet being placed. To use this only /auth and /debit-credit need to be used 

This is the preferable way, since it leaves no bets opened and is normally used with slot games etc. Any in-game buy feature like buy free spins need to implement separate /debit for buy and /credit for settlement . Using each endpoint separately   
This method can be used with live games when player places bets and results are only known after each round is finished. To use this /auth /debit /credit and /rollback can be used 

. If provider(Vendor) has bonus buy feature, and when customer starts to play it we expect to receive DEBIT to start the round which should stay with open status until its not played and we expect to receive CREDIT request only when this feature is finished. 

Balance vs Bonus Balance 

One of the features of our system is bonus. To simplify, this is another customers wallet that is used to receive promotions etc. and is depleted before regular balance. Meaning, that if customer has bonus balance, debit amount would be taken from that balance and if bet amount would exceed available bonus balance, regular balance would be used. Proportional to this players winnings are split between both balances. 

If possible, bonus balance should be shown separately. If this is not possible, regular balance and bonus balance should be added together and their sum should be showed. 

Intro 

All requests should use https. 

All requests should be called with POST and JSON body. 

All requests should include the header called Generic-Id with the predefined value agreed by both parties. All responses contain a JSON in the body of the response. 

Data needed to send requests 

| name  | value  | example |
| ----- | :---- | :---- |
| baseEngineURL  | provided by our team  | http://casinoengine.test.pronetgaming.com/ |
| vendorCode  | provided by our team  | znidi\_gaming |
| genericId  | provided by our team  | 46b0da0cd81423dcdac17d2070b4af16 |
| genericSecretK ey | provided by our team  | 86b04d46bb0e81a1131c6e6acd2b7e75 |
| genericOpenGa meToken | sent as token url  parameter in open game request | 692A5DF3996581AD15F3BE7DE0C02ECD908471949ABBD95CC5DDC05AAB DB15FE94A06BB16ED0F8DB |

Postman  
We also prepared Postman calls that you should have received along this documentation (Generic Api V5.postman\_collection.json) 

You will also need to import the environment our team will provide. Most of the environmental variables match above table "Data needed to send requests". 

Since our postman collection contains hash calculations, it can serve as a code example. Response schema and other useful things can also be found under tests etc. 

Postman includes /getToken request, that mimics opening game and passing token to wallet side and can only be used in QA/development environment. 

Note: genericSecretKey is not sent over with request nor should be, collection uses it for hash generation in pre-request script. Legend Of Types in JSON   
Request Fields

| Field  | Type  | Description |
| :---- | :---- | :---- |
| customer  | String  | Customer playing the game |
| token  | String  | For customer validation |
| hash  | String  | Used for request validation, we use HMAC\_SHA\_256(explained in  document) |
| amount  | Double  | Amount player played/won |
| creditAmount  | Double  | Amount player won or 0 if round was lost Only used with /debit-credit) |
| betId  | String  | Some call it round ID, betId has to be at least unique per player |
| trxId  | String  | Transaction id of a bet. It has to be unique. One betId can have multiple trxId, like for instance in the case of BlackJack where the player can raise his bet. |
| creditTrxId  | String  | Transaction Id of credit part of the bet Only used with /debit-credit) |
| tip  | boolean  | Used in Live Games where you can tip the dealer |
| freespin  | JSON  | Json object with additional data (used on /credit requests only) listed below with  |
| freespinRef  | String  | Used and stored as a reference to either game provider based or API given freespins |
| requested  | boolean  | Used to indicate that the freespin was given via our API (in such case, freespin data is also updated on our side) |

| remainingRounds  | Integer  | Indicates how many freespins with that ref remain to be used by the  customer/player |
| :---- | :---- | :---- |
| totalWinnings  | Double  | The combined amount won (up to that point) with freespins with this reference |

Response Fields 

| Field  |  | Type Description |
| ----- | ----- | :---- |
| code  | Integ er | Status code for the request |
| status  |  | String Status message for the request |
| currency  |  | String (ISO 4217 \- "TRY", "USD", "EUR", ... |
| balance  | Doubl e | Balance of the customer after the request has been processed |
| bonusBala nce | Doubl e | Bonus balance of the customer after the request has been processed |
| trxId  | Long  | Transaction id from our system(not present for /credit request with amount=0, because no transaction is done, only round gets closed) |
| creditTrxId Long  |  | Transaction id from our system Only used with /debit-credit when creditAmount was \> 0 ) |
| traderId  | Integ er | can be used for brand separation, available only in /auth response |

Calculating Hash 

Hash value will be calculated using SHA\_256. We will have exchange secretKey, that is used in the calculation. Note: hash field is case sensitive and should contain only lowercase letters and numbers 

Hash should be calculated by marshaling the request body json, concatenating genericSecretKey and applying SHA256 over the marshaled string. 

Note: working hash calculation scripts for all requests can be found in the postman collection (under Pre-request Script tab of each request) 

Example: 

Steps 

1 json \= { 

2 customer: "2020120400005" 

3 token: "D8ECE868830CE30D467D0D715414D81D3685A0368FD7CBE257FBAB6599D6BE8CD2D8777328F804FB" 4 gameId: "Test\_game" 

5 amount: "2" 

6 currency: "TRY" 

7 betId: 5334875 

8 trxId: 22198 

9 freespin: {  
10 freespinRef: "23a234de532" 

11 } 

12 } 

13 jsonString \= " 

{"customer":"2020120400005","token":"D8ECE868830CE30D467D0D715414D81D3685A0368FD7CBE257FBAB6599D6BE8CD2D877732 8F804FB","gameId":"Test\_game","amount":"2","currency":"TRY","betId":5334875,"trxId":22199,"freespin": {"freespinRef":"23a234de532"}}" 

14 toHashString \= " 

{"customer":"2020120400005","token":"D8ECE868830CE30D467D0D715414D81D3685A0368FD7CBE257FBAB6599D6BE8CD2D877732 8F804FB","gameId":"Test\_game","amount":"2","currency":"TRY","betId":5334875,"trxId":22200,"freespin": {"freespinRef":"23a234de532"}}55554525de8e724703abe221caef9c2e" 

15 hash \= 3aca48a20d7ec29080b90d82912e65e97e19287615e33f46016bded2a8776fb7 

JS Example code 

1 var genericSecretKey \= "55554525de8e724703abe221caef9c2e" 

2 var jsonString \= JSON.stringify(json); 

3 var toHash \= jsonString \+ genericSecretKey; 

4 var hash \= CryptoJS.SHA256(toHash).toString();

URL 

Endpoints should be accessible at (Generic Vendor API v5\#Dataneededtosendrequests): 

{{baseEngineURL/casino-engine/generic/{{vendorCode}}/v5/ 

Headers 

| name  | value |
| :---- | :---- |
| Content-Type  | application/json |
| Generic-Id  | genericId from Generic Vendor API  v5\#Dataneededtosendrequests |
| Hash  | requests calculated hash |

Methods 

There are 6 endpoints: 

/auth 

/debit-credit 

/credit 

/debit 

/rollback 

/promo 

We will provide for each one with example requests and responses: 

/auth 

This endpoint is used for getting balance 

request:   
1 { 

2 "customer":"2019105152683", 

3 "token":"692A5DF3996581ADD825BEB52A71F54F519AEEBCB2BA0DBCC3D440C2B98887A076D037C7F879D344" 4 } 

response: 

1 { 

2 "balance":120.548, 

3 "bonusBalance": 5.1, 

4 "code":0, 

5 "currency":"TRY", 

6 "status":"SUCCESS", 

7 "traderId": 1 

8 } 

/debit-credit 

This endpoint is used to create and settle bet at the same time. 

request: 

1 { 

2 "customer": "2019105152683", 

3 "token": "692A5DF3996581ADD825BEB52A71F54F519AEEBCB2BA0DBCC3D440C2B98887A076D037C7F879D344", 4 "gameId": "dummy\_record", 

5 "amount": 2, 

6 "creditAmount": 5, 

7 "currency": "TRY", 

8 "betId": "b3", 

9 "trxId": "d1", 

10 "creditTrxId": creditTrxId, 

11 "tip": null 

12 } 

response: 

1 { 

2 "balance": 120.548, 

3 "bonusBalance": 3.1, 

4 "code": 0, 

5 "currency": "TRY", 

6 "status": "SUCCESS", 

7 "trxId": 1222371857, 

8 "creditTrxId": 1222445716 

9 } 

/debit 

This endpoint is used in combination with /credit and /rollback. It creates a bet and lowers players balance/bonusBalance for the received amount 

request: 

1 { 

2 "customer": "2019105152683", 

3 "token": "692A5DF3996581ADD825BEB52A71F54F519AEEBCB2BA0DBCC3D440C2B98887A076D037C7F879D344", 4 "gameId": "dummy\_record", 

5 "amount": 2,  
6 "currency": "TRY", 

7 "betId": "b3", 

8 "trxId": "d1", 

9 "tip": null 

10 } 

response: 

1 { 

2 "balance": 118.548, 

3 "bonusBalance": 3.1, 

4 "code": 0, 

5 "currency": "TRY", 

6 "status": "SUCCESS", 

7 "trxId": 1222371857 

8 } 

/credit 

This endpoint is used in combination with /debit and /rollback. It creates a bet and increases players balance/bonusBalance for the received amount 

request: 

1 { 

2 "customer": "2019105152683", 

3 "token": "692A5DF3996581ADD825BEB52A71F54F519AEEBCB2BA0DBCC3D440C2B98887A076D037C7F879D344", 4 "gameId": "dummy\_record", 

5 "amount": 34.778, 

6 "currency": "TRY", 

7 "betId": "b3", 

8 "trxId": "c2" 

9 } 

response: 

1 { 

2 "balance": 188.104, 

3 "bonusBalance": 3.1, 

4 "code": 0, 

5 "currency": "TRY", 

6 "status": "SUCCESS", 

7 "trxId": ********** 

8 } 

/rollback 

This endpoint is used in combination with /debit and /credit. It increasese/decreases customers balance/bonusBalance for the amount of the transaction with this id. Note: the trxId in the request should be the trx from the vendor. The returned trxId from response is from our system. 

request: 

1 { 

2 "customer": "2019105152683", 

3 "token": "692A5DF3996581ADD825BEB52A71F54F519AEEBCB2BA0DBCC3D440C2B98887A076D037C7F879D344", 4 "trxId": "d15", 

5 "gameId": "dummy\_record" 

6 }  
response: 

1 { 

2 "balance": 182.0, 

3 "bonusBalance": 3.1, 

4 "code": 0, 

5 "currency": "TRY", 

6 "status": "SUCCESS", 

7 "trxId": 1222371865 

8 } 

/promo 

The promo endpoint includes all promotional wins: 

| promoType  | description |
| :---- | :---- |
| FSW  | freespin |
| JPW  | jackpot |
| CB  | cashBack |
| TW  | tournament win |
| RW  | reward |
| REW  | red envelope win |
| CDW  | cash drop win |
| RB  | rakeBack |

This endpoint can be called without previously opening bet with debit call. Transactions made with this version of the call will be marked differently in our system (as "promo type" wins) and a new bet is going be created with provided in request “betId”. 

Most of the promo requests (including freespin given from game provider backoffice) have the same structure. Just change the "promoType" to the desired value from the above table. The only difference is the "system freespin request (freespin given via API)". For this request there needs to be provided additional data (freeSpinData object). 

Note: if you will not provide the "gameId" in the promo request (e.g. for jackpot that was not connected to a particular game), please let us know so we insert a placeholder for it \- "game\_promotions". If this will not be done the "GAME\_NOT\_FOUND" message will be returned. Also if you cannot provide a valid token for a promotion (e.g. if the promotion happens separately from playing, like Tournament wins), we can add a configuration on our side, that for a particular promotion type, the "token" is not needed in the request. 

"promo" section in request body is mandatory as is the "promoType" inside it \- please send request like this body: example promo request for tournament win: 

1 { 

2 "customer": "2019105152683", 

3 "token": "692A5DF3996581ADD825BEB52A71F54F519AEEBCB2BA0DBCC3D440C2B98887A076D037C7F879D344", 4 "gameId": "dummy\_record", 

5 "amount": 34.778, 

6 "currency": "TRY", 

7 "betId": "b3", 

8 "trxId": "c2",  
9 "promo": { 

10 "promoType": "TW", 

11 "promoRef": "23a234de532" 

12 } 

13 } 

response: 

1 { 

2 "balance": 188.104, 

3 "bonusBalance": 3.1, 

4 "code": 0, 

5 "currency": "TRY", 

6 "status": "SUCCESS", 

7 "trxId": ********** 

8 } 

example promo request for freespin (freespin given from game provider backoffice): 

Transactions made with this promoType of the call will be marked differently in our system (as freespin wins) regardless of betId matching a debit or not. (If betId matches an existing bet, amount will be added to that bet, but transaction will be marked differently.) 

1 { 

2 "customer": "2019105152683", 

3 "token": "692A5DF3996581ADD825BEB52A71F54F519AEEBCB2BA0DBCC3D440C2B98887A076D037C7F879D344", 4 "gameId": "dummy\_record", 

5 "amount": 34.778, 

6 "currency": "TRY", 

7 "betId": "b3", 

8 "trxId": "c2", 

9 "promo": { 

10 "promoType": "FSW", 

11 "promoRef": "23a234de532" 

12 } 

13 } 

response: 

1 { 

2 "balance": 188.104, 

3 "bonusBalance": 3.1, 

4 "code": 0, 

5 "currency": "TRY", 

6 "status": "SUCCESS", 

7 "trxId": ********** 

8 } 

example promo request for system freespin (freespin given via API): 

Transactions made with this promoType of the call will be marked differently in our system (as freespin wins) regardless of betId matching a debit or not. (If betId matches an existing bet, amount will be added to that bet, but transaction will be marked differently.) 

Inside the freeSpinData json object there can be different parameters. More information about them can be found in section "Legend of types in JSON" → "freespin" above. 

1 { 

2 "customer": "2019105152683", 

3 "token": "692A5DF3996581ADD825BEB52A71F54F519AEEBCB2BA0DBCC3D440C2B98887A076D037C7F879D344",  
4 "gameId": "dummy\_record", 

5 "amount": 34.778, 

6 "currency": "TRY", 

7 "betId": "b3", 

8 "trxId": "c2", 

9 "promo": { 

10 "promoType": "FSW", 

11 "promoRef": "23a234de532" 

12 "freeSpinData": { 

13 "remainingRounds": 5, 

14 "totalWinnings": 100.8, 

15 "requested": true 

16 } 

17 } 

18 } 

response: 

1 { 

2 "balance": 188.104, 

3 "bonusBalance": 3.1, 

4 "code": 0, 

5 "currency": "TRY", 

6 "status": "SUCCESS", 

7 "trxId": ********** 

8 } 

Status Codes And Errors 

All of the above methods return a status code. In case of an error, we return an error message. The error message consists of code and status fields. 

Example 

1 { 

2 "code": \-20310, 

3 "status": "BET\_ALREADY\_SETTLED" 

4 }

List of all Status Codes 

| Status  | Co  de | Description  | Additional Action On Game Provider Side |
| :---- | ----- | :---- | :---- |
| "SUCCESS"  | 0  | Request was successful.  | Flag the request as settled. Stop sending the request. |
| "UNKNOWN\_ERR OR" | 1  | General error that does not fall into any of the other categories. | The vendor should try to resend the request in intervals. Example: 30s, 1min, 5min, 15min, 30min, 1h, 2h. If it fails after these actions contact Casino support or Casino Integration Team. |
| "UNAUTHORIZE D\_REQUEST" | 2  | Invalid hash.  | Stop sending the request and flag it as invalid. If it's happening to frequently contact Casino Support. |
| "NOT\_INTEGRAT ED" | 3  | Vendor not active.  | Stop sending the request and flag it as invalid. |

| "TOKEN\_CUSTO MER\_MISMATCH " | 4  | The received token was created for another customer. | Stop sending the request and check your code since this could mean serious security issues. |
| :---- | ----- | :---- | :---- |
| "UNSUPPORTED \_API\_VERSION" | 5  | The operation you are trying to call is not supported. | Contact support team for the newest api documentation. |
| "INTERNAL\_CAC HE\_ERROR" | 6  | One of our caches failed to get data from db. | Retry the request after some time. If the issue persists after retries, please contact Casino Support. |
| "PROMOTION\_T YPE\_NOT\_SUPPORTED" | 7  | Sent promotion is not supported in this version | Stop sending request and check your sent request/code (promotionType) |
| "BET\_RECORD\_N OT\_FOUND" | 2  01  20 | Record was not found in our system. | Stop sending the request and flag it as invalid. |
| "BET\_ALREADY\_ WON" | 2  011  2 | Bet was already settled.  | Flag the request as settled. Stop sending the request. |
| "AUTHENTICATI ON\_FAILED" | 2  01  01 | Problem when authenticating.  | The vendor should try to resend the request in intervals. Example: 30s, 1min, 5min, 15min, 30min, 1h, 2h. If it fails after these actions contact Casino support or Casino Integration Team. |
| "GAME\_NOT\_FO UND" | 2  01  30 | There is a miss-match between the game id in our system and your system. | Contact Casino Support. |
| "BET\_LIMIT\_REA CHED" | 2  02  01 | The player can no longer place bets. | Revert the round. Stop sending request. |
| "LOSS\_LIMIT\_RE ACHED" | 2  02  02 | The player can no longer place bets. | Revert the round. Stop sending request. |
| "SESSION\_LIMIT\_REACHED" | 2  02  03 | The player can no longer place bets. | Revert the round. Stop sending request. |
| "PROFIT\_LIMIT\_ REACHED" | 2  02  04 | The player can no longer place bets. | Revert the round. Stop sending request. |
| "INVALID\_CASINO\_VENDOR" | 2  03  01 | The game provider was not found on our side. | Contact Casino Support. Stop sending request. |
| "ALL\_BET\_ARE\_O FF" | 2  03  02 | This means betting is off. This is usually when we have  maintenance of which you will be informed prior. | Revert the round. Stop sending request. |

| "INVALID\_GAME " | 2  03  03 | There is a miss-match between the game id in our system and your system. | Contact Casino Support. |
| :---- | ----- | :---- | :---- |
| "CUSTOMER\_NO T\_FOUND" | 2  03  04 | The player was not found in our system. | The vendor should try to resend the request in intervals. Example: 30s, 1min, 5min, 15min, 30min, 1h, 2h. If it fails after these actions contact Casino support or Casino Integration Team. |
| "INVALID\_CURR ENCY" | 2  03  05 | The request contains a currency that we don't support(have  configured). | Stop sending the request and flag it as invalid. Contact Casino Support, so we see whats going on. |
| "INSUFFICIENT\_ FUNDS" | 2  03  06 | The player does not have enough balance to place a bet. | Stop sending the request and flag it as invalid. |
| "PLAYER\_SUSPE NDED" | 2  03  07 | The player is suspended.  | Revert the round. Stop sending the request and flag it as invalid. |
| "REQUIRED\_FIEL D\_MISSING" | 2  03  08 | Forgot to send a parameter in request. | Revert the round. Stop sending the request and flag it as invalid. Check documentation. If you can't find the error, contact Casino Support. |
| "DATA\_OUT\_OF\_RANGE" | 2  03  09 | Problem on our side.  | Stop sending request. Contact Casino Support. |
| "BET\_ALREADY\_ SETTLED" | 2  03  10 | Bet was already settled.  | Flag the request as settled. Stop sending the request. |
| "TOKEN\_NOT\_FOUND" | 2  03  16 | Customer with that token not found. | Stop sending request with old token. |
| "TOKEN\_TIMEOUT" | 2  03  11 | The token has timed-out.  | Stop sending request with old token. |
| "TOKEN\_INVALI D" | 2  03  12 | Same as "TOKEN\_TIMEOUT".  | Same as "TOKEN\_TIMEOUT". |
| "TRANSACTION\_ NOT\_FOUND" | 2  03  13 | Transaction is not found.  | Stop sending request. Contact support. |
| "NEGATIVE\_DEP OSIT" | 2  03  14 | The deposit value is negative.  | Stop sending request and check your request/code. |
| "NEGATIVE\_WIT HDRAWAL" | 2  03  15 | Negative withdrawal.  | Stop sending request and check your request/code. |

Round Details 

The vendor should implement a REST API, that accepts the below query parameters. We will always send all parameters listed here, even if you don't need all of them. 

The vendor will provide us the URL on which the implemented API is accessible. 

Query parameters 

| name  | type  | description  | example |
| ----- | :---: | :---- | :---- |
| customer  | String  | Our customer identifier  | 2019045569026 |
| roundId  | String  | Providers round identifier  | 56ceecfcbc213e12e4288931 |
| gameId  | String  | Providers game identifier (max 50 chars)  | test\_game |
| lang  | String  | Language code in ISO 6391 (default=en)  | en |

Example 

GET \<providers-url\>?customer=2019045569026\&roundId=56ceecfcbc213e12e4288931\&gameId=test\_game\&lang=en 

\!Depending on vendor you might need to include the trader param passed on openGame like it is done for Hacksaw → https://api.hacksawgaming.com/api/v1/pronet/pronet\_PG-{traderId}/replay-url? 

customer=2019045569026\&roundId=56ceecfcbc213e12e4288931\&gameId=test\_game\&lang=en 

Response 

The vendor can choose what to return: 

raw JSON round details data 

image round details data 

HTML formatted round details data 

JSON schema 

JSON schema 

1 { 

2 "$ref": "\#/$defs/roundDetails", 

3 "$defs": { 

4 "roundDetails": { 

5 "type": "object", 

6 "additionalProperties": false, 

7 "properties": { 

8 "code": { 

9 "type": "number", 

10 "enum": \[ 

11 0, 

12 \-1, 

13 \-2 

14 \] 

15 }, 

16 "status": { 

17 "type": "string", 

18 "enum": \[ 

19 "SUCCESS",  
20 "UNKNOWN\_ERROR", 

21 "NOT\_FOUND" 

22 \] 

23 }, 

24 "json": { 

25 "$ref": "\#/$defs/jsonRoundDetails" 26 }, 

27 "image": { 

28 "$ref": "\#/$defs/imageRoundDetails" 29 }, 

30 "html": { 

31 "$ref": "\#/$defs/htmlRoundDetails" 32 } 

33 }, 

34 "required": \[ 

35 "code", 

36 "status" 

37 \], 

38 "oneOf": \[ 

39 { 

40 "required": \[ 

41 "json" 

42 \] 

43 }, 

44 { 

45 "required": \[ 

46 "image" 

47 \] 

48 }, 

49 { 

50 "required": \[ 

51 "html" 

52 \] 

53 } 

54 \] 

55 }, 

56 "jsonRoundDetails": { 

57 "type": "object", 

58 "additionalProperties": false, 59 "properties": { 

60 "gameId": { 

61 "type": "string" 

62 }, 

63 "gameName": { 

64 "type": "string" 

65 }, 

66 "roundId": { 

67 "type": "string" 

68 }, 

69 "roundDate": { 

70 "type": "string", 

71 "format": "date-time" 72 }, 

73 "betAmount": { 

74 "type": "number" 

75 }, 

76 "winAmount": { 

77 "type": "number"  
78 }, 

79 "currency": { 

80 "type": "string" 

81 } 

82 }, 

83 "required": \[ 

84 "gameId", 

85 "gameName", 

86 "roundId", 

87 "roundDate", 

88 "betAmount", 

89 "winAmount", 

90 "currency" 

91 \] 

92 }, 

93 "imageRoundDetails": { 

94 "type": "object", 

95 "additionalProperties": false, 

96 "properties": { 

97 "url": { 

98 "type": "string" 

99 }, 

100 "height": { 

101 "type": "number" 

102 }, 

103 "width": { 

104 "type": "number" 

105 } 

106 }, 

107 "required": \[ 

108 "url" 

109 \], 

110 "$comment": "Optionally specify the dimensions of the image for display." 111 }, 

112 "htmlRoundDetails": { 

113 "type": "object", 

114 "additionalProperties": false, 

115 "properties": { 

116 "content": { 

117 "type": "string", 

118 "$comment": "Escaped HTML content." 

119 }, 

120 "height": { 

121 "type": "number" 

122 }, 

123 "width": { 

124 "type": "number" 

125 } 

126 }, 

127 "required": \[ 

128 "content" 

129 \], 

130 "$comment": "Optionally specify the dimensions of the HTML content for display." 131 } 

132 } 

133 } 

134  
Example raw JSON response 

JSON round details 

1 { 

2 "code": 0, 

3 "status": "SUCCESS", 

4 "json": { 

5 "gameId": "123421", 

6 "gameName": "Some game", 

7 "roundId": "56ceecfcbc213e12e4288931", 

8 "roundDate": "2016-02-29T16:03:24.003Z", 

9 "betAmount": 1.5, 

10 "winAmount": 3.0, 

11 "currency": "EUR" 

12 } 

13 } 

Example image response 

JSON with image round details 

1 { 

2 "code": 0, 

3 "status": "SUCCESS", 

4 "image": { 

5 "url": "https://example.vendor.org/round-details/123abc", 

6 "height": 600, 

7 "width": 300 

8 } 

9 } 

Example HTML response 

JSON with image round details 

1 { 

2 "code": 0, 

3 "status": "SUCCESS", 

4 "html": { 

5 "content": "\<p style=\\"color: red;\\"\>Round details\</p\>", 

6 "height": 600, 

7 "width": 300 

8 } 

9 } 

Status Codes And Errors 

All responses return a status code. In case of an error the vendor should return an error message. The error message consists of code and status fields. 

Example 

JSON error message 

1 { 

2 "code": \-2, 

3 "status": "NOT\_FOUND" 

4 }  
List of all Status Codes 

| Status  | Code  | Description |
| :---- | :---- | :---- |
| "SUCCESS"  | 0  | Request was successful. |
| "UNKNOWN\_ERROR"  | 1  | General error that does not fall into any of the other categories. |
| "NOT\_FOUND"  | 2  | Round details not found. |

Attachments 

File Modified File Generic Api V5.postman\_collection.json Feb 18, 2025 by Vid Rotar PDF File PCESGeneric Vendor API v5131124120103.pdf Nov 13, 2024 by Vid Rotar File Generic API V5 Generic 117.postman\_environment.json Nov 13, 2024 by Vid Rotar 

Drag and drop to upload or browse for files![][image1] 

Download All

[image1]: <data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAsAAAALCAYAAACprHcmAAABUElEQVR4XpXRMWvCQBQH8Ld19DukX8GlCNZFqoiThhLXLlYQ6SBkTECdihUCOlha6Ka46CDGKZm0iAYNgYAgiilBTnToKlzfE+nuwY9b/rn7vxwArn6/L7RaLYM4jvMdDAb5crn8ZIyFSSwWoxiAruvCfD7Pp1IpRkrlEocb4IvF4tfzPJ3IsgzxeByg1+sZkiQxRVWOJBQJnaIPUcu2bcv3/Rey3W5f9/t9+LqwaZpyo9Hg6cf0icxms7fscxZUVQXs+0V2u90ThgOQy+V4IpHglUrFIsPh8DyLKIoUvieHw4ETGI1Gcrvd5oqinAgOJiWTScAat6vVyr2oYZUAdS4Ui0WGHxxJvV7fTCYTazweb6bT6QfBA8z1ep25LjwYDATXdfOapjFSrVbfm80mx+41wzB+CP7rCA4u/D9Mp9MpEHzNO9x5t9sN4A2Zi3PwD3PXMUQ/ljHwAAAAAElFTkSuQmCC>