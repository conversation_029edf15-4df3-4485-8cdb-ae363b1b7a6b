{"compilerOptions": {"target": "es2021", "lib": ["es2021"], "module": "NodeNext", "moduleResolution": "NodeNext", "esModuleInterop": false, "outDir": "lib", "allowSyntheticDefaultImports": true, "sourceMap": true, "isolatedModules": false, "experimentalDecorators": true, "emitDecoratorMetadata": true, "declaration": false, "noImplicitAny": false, "removeComments": true, "noLib": false, "preserveConstEnums": true, "suppressImplicitAnyIndexErrors": true, "inlineSources": false, "skipLibCheck": true, "baseUrl": "./", "paths": {"@entities/*": ["src/entities/*"], "@errors/*": ["src/errors/*"], "@utils/*": ["src/utils/*"], "@config": ["src/config"], "@wallet/*": ["src/wallet/*"], "@payment/*": ["src/wallet/payment/*"], "@launcher/*": ["src/launcher/*"], "@operator/*": ["src/operator/*"], "@mock/*": ["src/mock/*"]}}, "include": ["src/**/*.ts"], "exclude": ["node_modules"]}