# SW Integration Implementation Templates

This document provides ready-to-use code templates for implementing integration systems based on the sw-integration-b365 architecture.

## Project Setup Templates

### 1. Package.json Template

```json
{
  "name": "sw-integration-[operator-name]",
  "version": "1.0.0",
  "description": "SW Integration for [Operator Name]",
  "author": "Your Name",
  "private": true,
  "license": "UNLICENSED",
  "scripts": {
    "clean": "rm -rf ./lib",
    "build": "npm run version && nest build",
    "format": "prettier --write \"**/*.ts\"",
    "start:launcher": "nest start launcher",
    "start:launcher:dev": "MEASURES_PROVIDER=prometheus nest start launcher --watch",
    "start:operator": "nest start operator",
    "start:operator:dev": "MEASURES_PROVIDER=prometheus nest start operator --watch",
    "start:wallet": "nest start wallet",
    "start:wallet:dev": "MEASURES_PROVIDER=prometheus nest start wallet --watch",
    "start:mock": "nest start mock",
    "test": "mocha -r ts-node/register --exit src/**/*.spec.ts",
    "test:watch": "npm run test -- --watch",
    "version": "node -e \"require('fs').writeFileSync('lib/version', require('./package.json').version)\""
  },
  "dependencies": {
    "@nestjs/common": "^7.4.2",
    "@nestjs/core": "^7.4.2",
    "@nestjs/platform-fastify": "^7.0.9",
    "@nestjs/schedule": "^1.1.0",
    "@nestjs/typeorm": "^7.1.5",
    "@skywind-group/sw-integration-core": "^1.0.82",
    "@skywind-group/sw-utils": "^1.0.4",
    "@skywind-group/sw-wallet-adapter-core": "^0.6.194",
    "class-transformer": "^0.3.1",
    "class-validator": "^0.12.2",
    "fastify": "^2.14.1",
    "module-alias": "^2.2.2",
    "pg": "^8.3.3",
    "typeorm": "^0.2.45"
  },
  "devDependencies": {
    "@nestjs/testing": "^7.4.2",
    "@types/chai": "^4.2.12",
    "@types/mocha": "^7.0.2",
    "@types/node": "^7.10.9",
    "chai": "^4.2.0",
    "mocha": "^7.1.2",
    "mocha-typescript": "^1.1.17",
    "prettier": "^2.0.5",
    "ts-node": "^8.10.2",
    "typescript": "^3.9.7"
  },
  "_moduleAliases": {
    "@entities": "src/entities",
    "@errors": "src/errors",
    "@utils": "src/utils",
    "@config": "src/config",
    "@wallet": "src/wallet",
    "@payment": "src/wallet/payment",
    "@launcher": "src/launcher",
    "@operator": "src/operator",
    "@mock": "src/mock"
  }
}
```

### 2. TypeScript Configuration

```json
{
  "compilerOptions": {
    "target": "es2021",
    "lib": ["es2021"],
    "module": "NodeNext",
    "moduleResolution": "NodeNext",
    "esModuleInterop": false,
    "outDir": "lib",
    "allowSyntheticDefaultImports": true,
    "sourceMap": true,
    "isolatedModules": false,
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
    "declaration": false,
    "noImplicitAny": false,
    "removeComments": true,
    "noLib": false,
    "preserveConstEnums": true,
    "suppressImplicitAnyIndexErrors": true,
    "inlineSources": false,
    "skipLibCheck": true,
    "baseUrl": "./",
    "paths": {
      "@entities/*": ["src/entities/*"],
      "@errors/*": ["src/errors/*"],
      "@utils/*": ["src/utils/*"],
      "@config": ["src/config"],
      "@wallet/*": ["src/wallet/*"],
      "@payment/*": ["src/wallet/payment/*"],
      "@launcher/*": ["src/launcher/*"],
      "@operator/*": ["src/operator/*"],
      "@mock/*": ["src/mock/*"]
    }
  },
  "include": ["src/**/*.ts"],
  "exclude": ["node_modules"]
}
```

### 3. NestJS CLI Configuration

```json
{
  "collection": "@nestjs/schematics",
  "sourceRoot": "src",
  "projects": {
    "wallet": {
      "type": "application",
      "entryFile": "mainWallet"
    },
    "launcher": {
      "type": "application",
      "entryFile": "mainLauncher"
    },
    "operator": {
      "type": "application",
      "entryFile": "mainOperator"
    },
    "mock": {
      "type": "application",
      "entryFile": "mainMock"
    }
  }
}
```

## Core Service Templates

### 1. Main Service Entry Points

#### Wallet Service (src/mainWallet.ts)

```typescript
// this should be the first line
import { measures } from "@skywind-group/sw-utils";
// this should be the second line
measures.measureProvider.baseInstrument();
import "module-alias/register";
import { bootstrapServer } from "@skywind-group/sw-integration-core";
import { WalletModule } from "@wallet/wallet.module";
import config from "@config";

bootstrapServer({
    serviceName: "sw-[operator]-wallet",
    versionFile: "./lib/version",
    module: WalletModule,
    internalPort: config.internalServer.port,
    port: config.server.walletPort,
    secureKeys: config.securedKeys
});
```

#### Launcher Service (src/mainLauncher.ts)

```typescript
// this should be the first line
import { measures } from "@skywind-group/sw-utils";
// this should be the second line
measures.measureProvider.baseInstrument();
import "module-alias/register";
import { bootstrapServer } from "@skywind-group/sw-integration-core";
import config from "@config";
import { LauncherModule } from "@launcher/launcher.module";

bootstrapServer({
    serviceName: "sw-[operator]-launcher",
    versionFile: "./lib/version",
    module: LauncherModule,
    internalPort: config.internalServer.port,
    port: config.server.launcherPort,
    secureKeys: config.securedKeys
});
```

#### Operator Service (src/mainOperator.ts)

```typescript
// this should be the first line
import { measures } from "@skywind-group/sw-utils";
// this should be the second line
measures.measureProvider.baseInstrument();
import "module-alias/register";
import { bootstrapServer } from "@skywind-group/sw-integration-core";
import config from "@config";
import { OperatorModule } from "@operator/modules/operator.module";

bootstrapServer({
    serviceName: "sw-[operator]-operator",
    versionFile: "./lib/version",
    module: OperatorModule,
    internalPort: config.internalServer.port,
    port: config.server.operatorPort,
    secureKeys: config.securedKeys
});
```

### 2. Configuration Template (src/config.ts)

```typescript
import { HttpGatewayConfig } from "@skywind-group/sw-integration-core";

const http: HttpGatewayConfig = {
    operatorUrl: process.env.OPERATOR_HTTP_URL || "https://api.operator.com",
    defaultOptions: {
        timeout: +process.env.OPERATOR_HTTP_TIMEOUT || 5000,
        proxy: process.env.OPERATOR_HTTP_PROXY
    },
    keepAlive: {
        maxFreeSockets: +process.env.OPERATOR_HTTP_KEEP_ALIVE_MAX_FREE_SOCKETS || 100,
        socketActiveTTL: +process.env.OPERATOR_HTTP_KEEP_ALIVE_SOCKET_TTL || 60000,
        freeSocketKeepAliveTimeout: +process.env.OPERATOR_HTTP_KEEP_ALIVE_TIMEOUT || 30000
    },
    ssl: {
        ca: process.env.OPERATOR_SSL_CA,
        key: process.env.OPERATOR_SSL_KEY,
        cert: process.env.OPERATOR_SSL_CERT,
        password: process.env.OPERATOR_SSL_PASSWORD
    }
};

const db = {
    database: process.env.PGDATABASE || "management",
    user: process.env.PGUSER,
    password: process.env.PGPASSWORD,
    host: process.env.PGHOST || "localhost",
    port: +process.env.PGPORT || 5432,
    ssl: {
        isEnabled: process.env.PG_SECURE_CONNECTION === "true",
        ca: process.env.PG_CA_CERT || "./ca.pem"
    },
    maxConnections: +process.env.PG_MAX_CONNECTIONS || 10,
    maxIdleTime: +process.env.PG_MAX_IDLE_TIME_MS || 30000,
    schema: process.env.PGSCHEMA || "public",
    syncOnStart: process.env.SYNC_ON_START === "true",
    logEnabled: process.env.PG_MASTER_LOGGING === "true",
    cache: {
        isEnabled: process.env.PG_MASTER_CACHE === "true",
        ttl: +process.env.PG_MASTER_CACHE_TTL || 30000
    },
    connectionTimeoutInMs: +process.env.PG_MASTER_CONNECTION_TIMEOUT_MS || 10000
};

const config = {
    environment: process.env.NODE_ENV || "development",
    
    isProduction: (): boolean => {
        return config.environment === "production";
    },

    server: {
        walletPort: +process.env.SERVER_WALLET_PORT || 3000,
        launcherPort: +process.env.SERVER_LAUNCHER_PORT || 3001,
        operatorPort: +process.env.SERVER_OPERATOR_PORT || 3002,
        mockPort: +process.env.SERVER_MOCK_PORT || 3003
    },

    internalServer: {
        port: +process.env.INTERNAL_SERVER_PORT || 4054,
        api: {
            isEnabled: process.env.INTERNAL_API === "true"
        }
    },

    logging: {
        logLevel: process.env.LOG_LEVEL || "info",
        loggingOutput: (process.env.LOGGING_OUTPUT_TYPE || "console") as any,
        rootLogger: process.env.LOGGING_ROOT_LOGGER || "sw-integration-api",
        defaultSecureKeys: [
            "password", "newPassword", "key", "token", "accessToken", 
            "secretKey", "privateToken", "sharedKey"
        ]
    },

    merchantType: process.env.MERCHANT_TYPE || "[operator-code]",
    merchantCode: process.env.MERCHANT_CODE || "[operator-code]",
    defaultPlayerIp: process.env.DEFAULT_PLAYER_IP || "127.0.0.1",
    defaultJurisdiction: process.env.DEFAULT_JURISDICTION || "UK",
    
    currencyUnitMultiplier: +process.env.CURRENCY_UNIT_MULTIPLIER || 100,
    messageIdNumberLength: +process.env.MESSAGE_ID_NUMBER_LENGTH || 9,
    operatorApiVersion: process.env.OPERATOR_API_VERSION || "1.0",
    
    securedKeys: ["password", "username", "privateToken", "sharedKey"],

    http,
    db
};

export default config;
```

## Entity Templates

### 1. Integration Entities (src/entities/integration.entities.ts)

```typescript
import {
    MerchantGameInitRequest,
    MerchantGameTokenData,
    MerchantStartGameTokenData,
    PaymentRequest,
    PlayMode
} from "@skywind-group/sw-wallet-adapter-core";
import {
    BrokenGameRequest,
    CommitBonusPaymentRequest,
    GetFreeBetInfoRequest,
    KeepAliveRequest,
    RegulatoryActionRequest,
    TransferRequest
} from "@skywind-group/sw-integration-core";
import { IsDefined, IsIn, IsNotEmpty, IsOptional } from "class-validator";

// Operator-specific enums and interfaces
export enum OperatorJurisdiction {
    UK = "UK",
    IT = "IT",
    ES = "ES"
}

export enum OperatorPlatformType {
    desktop = "desktop",
    mobile = "mobile"
}

export interface OperatorGameTokenData {
    channelId: number;
    jurisdiction: string;
    privateToken?: string;
    freeSpinToken?: string;
    siteUrl?: string;
    disablePlayerPhantomFeatures?: boolean;
}

export interface OperatorGameLaunchData {
    gameCode: string;
    playmode: PlayMode;
    PublicToken?: string;
    language: string;
    currency: string;
    jurisdiction: OperatorJurisdiction;
    platform: OperatorPlatformType;
    historyUrl?: string;
    lobbyUrl?: string;
    cashierUrl?: string;
    merchantCode?: string;
    merchantType?: string;
}

// Integration-specific entities
export class IntegrationGameLaunchRequest implements OperatorGameLaunchData {
    @IsDefined()
    @IsNotEmpty()
    gameCode: string;

    @IsDefined()
    @IsIn([PlayMode.REAL, PlayMode.FUN])
    playmode: PlayMode;

    @IsOptional()
    @IsNotEmpty()
    PublicToken?: string;

    @IsDefined()
    @IsNotEmpty()
    language: string;

    @IsDefined()
    @IsNotEmpty()
    currency: string;

    @IsDefined()
    @IsIn([OperatorJurisdiction.UK, OperatorJurisdiction.IT, OperatorJurisdiction.ES])
    jurisdiction: OperatorJurisdiction;

    @IsIn([OperatorPlatformType.desktop, OperatorPlatformType.mobile])
    platform: OperatorPlatformType;

    @IsOptional()
    @IsNotEmpty()
    historyUrl?: string;

    @IsOptional()
    @IsNotEmpty()
    lobbyUrl?: string;

    @IsOptional()
    @IsNotEmpty()
    cashierUrl?: string;

    @IsOptional()
    merchantCode?: string;

    @IsOptional()
    @IsNotEmpty()
    merchantType?: string;
}

export interface IntegrationStartGameTokenData extends MerchantStartGameTokenData, OperatorGameTokenData {
    relaunchUrl?: string;
    loginFailed?: boolean;
}

export interface IntegrationGameTokenData extends MerchantGameTokenData, OperatorGameTokenData {}

export interface IntegrationInitRequest extends MerchantGameInitRequest, OperatorGameLaunchData {
    previousStartTokenData?: IntegrationStartGameTokenData;
    channelId: number;
    history_url?: string;
    platform?: string;
}

export interface IntegrationPaymentRequest extends CommitBonusPaymentRequest<IntegrationGameTokenData> {
    request: PaymentRequest;
    operatorRoundId?: string;
}

export interface IntegrationGetBalanceRequest {
    gameTokenData: IntegrationGameTokenData;
    merchantInfo: any;
}

export interface IntegrationTransferRequest extends TransferRequest<IntegrationGameTokenData> {}

export interface IntegrationKeepAliveRequest extends KeepAliveRequest<IntegrationGameTokenData> {}

export interface IntegrationBrokenGameRequest extends BrokenGameRequest<IntegrationGameTokenData> {}

export interface IntegrationGetFreeBetInfoRequest extends GetFreeBetInfoRequest<IntegrationGameTokenData> {}

export interface IntegrationRegulatoryActionRequest extends RegulatoryActionRequest<IntegrationGameTokenData> {}
```

### 2. Operator Entities (src/entities/operator.entities.ts)

```typescript
import { IsDefined, IsNotEmpty, IsOptional } from "class-validator";

// Base operator request/response interfaces
export interface OperatorBaseRequest {
    MessageID: number;
    UTCTimeStamp: string;
}

export interface OperatorBaseResponse {
    MessageID: number;
    UTCTimeStamp: string;
    ErrorDetails: OperatorError | null;
}

export interface OperatorError {
    ErrorCode: number;
    ErrorMessage: string;
}

// Authentication
export interface OperatorLoginRequest extends OperatorBaseRequest {
    PublicToken: string;
}

export interface OperatorLoginResponse extends OperatorBaseResponse {
    PrivateToken: string;
    GamingId: string;
    CurrencyCode: string;
    Balance: OperatorBalance;
    AllowOffers: boolean;
}

// Balance operations
export interface OperatorGetBalanceRequest extends OperatorBaseRequest {
    PrivateToken: string;
    GamingId: string;
}

export interface OperatorGetBalanceResponse extends OperatorBaseResponse {
    Balance: OperatorBalance;
    CurrencyCode: string;
}

export interface OperatorBalance {
    TotalAmount: number;
    CashAmount?: number;
    BonusAmount?: number;
    FreeSpinAmount?: number;
}

// Transaction operations
export enum OperatorTransactionType {
    Stake = "Stake",
    Return = "Return",
    Refund = "Refund"
}

export enum OperatorActionType {
    Standard = "Standard",
    FreeSpin = "FreeSpin",
    Bonus = "Bonus"
}

export interface OperatorTransactionRequest extends OperatorBaseRequest {
    PrivateToken: string;
    TransactionType: OperatorTransactionType;
    ActionType: OperatorActionType;
    Amount: number;
    CurrencyCode: string;
    ExternalTransactionID: string;
    GameRoundID?: string;
}

export interface OperatorTransactionResponse extends OperatorBaseResponse {
    Balance: OperatorBalance;
    CurrencyCode: string;
    AmountInGBP?: number;
}

// Round management
export interface OperatorStartGameRoundRequest extends OperatorBaseRequest {
    PrivateToken: string;
    GameCode: string;
    ChannelID: number;
    StartTimeUTC: string;
    ExternalGameRoundID: string;
}

export interface OperatorStartGameRoundResponse extends OperatorBaseResponse {
    GameRoundID: string;
}

export interface OperatorEndGameRoundRequest extends OperatorBaseRequest {
    PrivateToken: string;
    GameRoundID: string;
    EndTimeUTC: string;
}

export interface OperatorEndGameRoundResponse extends OperatorBaseResponse {
    Success: boolean;
}

// History
export class OperatorHistoryRequest {
    @IsDefined()
    @IsNotEmpty()
    publicToken: string;

    @IsDefined()
    @IsNotEmpty()
    jurisdiction: string;

    @IsOptional()
    @IsNotEmpty()
    language: string;

    @IsOptional()
    @IsNotEmpty()
    roundId?: number;

    @IsOptional()
    @IsNotEmpty()
    swRoundId?: string;

    @IsOptional()
    @IsNotEmpty()
    merchantCode?: string;

    @IsOptional()
    @IsNotEmpty()
    merchantType?: string;
}

// Error codes specific to operator
export const operatorErrorCodes = {
    TechnicalError: 1000,
    AuthenticationFailed: 1001,
    InsufficientFunds: 1002,
    TransactionNotFound: 1003,
    InvalidAmount: 1004,
    InvalidTransactionType: 1005,
    GamingLimits: 1006,
    PlayerSuspended: 1007,
    InvalidGameRound: 1008
};

// Transaction postfixes for external transaction IDs
export enum TransactionPostfix {
    BET = "_BET",
    WIN = "_WIN",
    REFUND = "_REFUND",
    AWARD_PROMO = "_AWARD_PROMO"
}
```

## HTTP Handler Templates

### 1. Base HTTP Handler (src/utils/baseHttp.handler.ts)

```typescript
import { HttpHandler, HTTPOperatorRequest } from "@skywind-group/sw-integration-core";
import { MerchantInfo } from "@skywind-group/sw-wallet-adapter-core";
import * as superagent from "superagent";
import config from "@config";
import { OperatorBaseRequest, OperatorJurisdiction } from "@entities/operator.entities";
import { mapOperatorToSWError } from "@errors/operator.errors";
import { generateNumber } from "@utils/common";

export interface HttpHandlerRequest {
    path: string;
    method: "get" | "post" | "put" | "delete";
    payload?: any;
    jurisdiction?: string;
    merchantInfo: MerchantInfo;
    retryAvailable?: boolean;
}

export abstract class BaseHttpHandler {
    protected buildBaseRequest(): OperatorBaseRequest {
        return {
            MessageID: generateNumber(config.messageIdNumberLength),
            UTCTimeStamp: new Date().toISOString()
        };
    }

    protected buildHttpRequest(request: HttpHandlerRequest): HTTPOperatorRequest {
        const url = this.buildUrl(request.path, request.jurisdiction);

        return {
            url,
            method: request.method,
            headers: this.buildHeaders(request.merchantInfo),
            payload: request.payload,
            timeout: config.http.defaultOptions.timeout,
            proxy: config.http.defaultOptions.proxy,
            ssl: config.http.ssl,
            retryAvailable: request.retryAvailable !== false
        };
    }

    protected parseHttpResponse<T>(response: superagent.Response): T {
        if (response.body.ErrorDetails) {
            throw mapOperatorToSWError(response.body.ErrorDetails, response);
        }
        return response.body as T;
    }

    protected getGameTokenData(req: any): any {
        return req.gameTokenData || req.request?.gameTokenData;
    }

    private buildUrl(path: string, jurisdiction?: string): string {
        const baseUrl = config.http.operatorUrl;
        const versionPath = `/v${config.operatorApiVersion}`;
        return `${baseUrl}${versionPath}/${path}`;
    }

    private buildHeaders(merchantInfo: MerchantInfo): { [key: string]: string } {
        return {
            "Content-Type": "application/json",
            "User-Agent": `SW-Integration/${merchantInfo.code}`,
            "X-Merchant-Code": merchantInfo.code
        };
    }
}

export const successResponses = [200, 201, 202];
```

### 2. Balance HTTP Handler (src/wallet/payment/balance.http.handler.ts)

```typescript
import { HttpHandler, HTTPOperatorRequest } from "@skywind-group/sw-integration-core";
import { Balance } from "@skywind-group/sw-wallet-adapter-core";
import * as superagent from "superagent";
import { BaseHttpHandler, HttpHandlerRequest } from "@utils/baseHttp.handler";
import {
    IntegrationGetBalanceRequest,
    OperatorGetBalanceRequest,
    OperatorGetBalanceResponse
} from "@entities/integration.entities";
import { Injectable } from "@nestjs/common";

@Injectable()
export class BalanceHttpHandler extends BaseHttpHandler
    implements HttpHandler<IntegrationGetBalanceRequest, Balance> {

    public async build(req: IntegrationGetBalanceRequest): Promise<HTTPOperatorRequest> {
        const gameTokenData = this.getGameTokenData(req);

        const balanceRequest: OperatorGetBalanceRequest = {
            ...this.buildBaseRequest(),
            PrivateToken: gameTokenData.privateToken,
            GamingId: gameTokenData.playerCode
        };

        return this.buildHttpRequest({
            path: "Balance",
            method: "get",
            payload: balanceRequest,
            jurisdiction: gameTokenData.jurisdiction,
            merchantInfo: req.merchantInfo,
            retryAvailable: true
        } as HttpHandlerRequest);
    }

    public async parse(response: superagent.Response): Promise<Balance> {
        const operatorResponse = this.parseHttpResponse<OperatorGetBalanceResponse>(response);

        return {
            totalAmount: operatorResponse.Balance.TotalAmount,
            cashAmount: operatorResponse.Balance.CashAmount || 0,
            bonusAmount: operatorResponse.Balance.BonusAmount || 0,
            currency: operatorResponse.CurrencyCode
        };
    }
}
```

### 3. Bet HTTP Handler (src/wallet/payment/bet.http.handler.ts)

```typescript
import { CommitPaymentRequest, HttpHandler, HTTPOperatorRequest } from "@skywind-group/sw-integration-core";
import { IntegrationGameTokenData, IntegrationPaymentRequest } from "@entities/integration.entities";
import {
    OperatorActionType,
    OperatorTransactionRequest,
    OperatorTransactionResponse,
    OperatorTransactionType,
    TransactionPostfix
} from "@entities/operator.entities";
import { BaseHttpHandler, HttpHandlerRequest } from "@utils/baseHttp.handler";
import * as superagent from "superagent";
import { Balance } from "@skywind-group/sw-wallet-adapter-core";
import { sumMajorUnits } from "@utils/common";
import { Injectable } from "@nestjs/common";

@Injectable()
export class BetHttpHandler extends BaseHttpHandler
    implements HttpHandler<CommitPaymentRequest<IntegrationGameTokenData>, Balance> {

    public async build(req: IntegrationPaymentRequest): Promise<HTTPOperatorRequest> {
        const betRequest: OperatorTransactionRequest = {
            ...this.buildBaseRequest(),
            PrivateToken: req.gameTokenData.privateToken,
            TransactionType: OperatorTransactionType.Stake,
            ActionType: OperatorActionType.Standard,
            Amount: sumMajorUnits(req.request.bet),
            CurrencyCode: req.gameTokenData.currency,
            ExternalTransactionID: req.request.transactionId.publicId + TransactionPostfix.BET,
            GameRoundID: req.operatorRoundId
        };

        return this.buildHttpRequest({
            path: "Transaction",
            method: "post",
            payload: betRequest,
            jurisdiction: req.gameTokenData.jurisdiction,
            merchantInfo: req.merchantInfo,
            retryAvailable: true
        } as HttpHandlerRequest);
    }

    public async parse(response: superagent.Response): Promise<Balance> {
        const operatorResponse = this.parseHttpResponse<OperatorTransactionResponse>(response);

        return {
            totalAmount: operatorResponse.Balance.TotalAmount,
            cashAmount: operatorResponse.Balance.CashAmount || 0,
            bonusAmount: operatorResponse.Balance.BonusAmount || 0,
            currency: operatorResponse.CurrencyCode
        };
    }
}
```

### 4. Win HTTP Handler (src/wallet/payment/win.http.handler.ts)

```typescript
import { CommitPaymentRequest, HttpHandler, HTTPOperatorRequest } from "@skywind-group/sw-integration-core";
import { IntegrationGameTokenData, IntegrationPaymentRequest } from "@entities/integration.entities";
import { BaseHttpHandler, HttpHandlerRequest } from "@utils/baseHttp.handler";
import * as superagent from "superagent";
import { Balance } from "@skywind-group/sw-wallet-adapter-core";
import {
    OperatorActionType,
    OperatorTransactionRequest,
    OperatorTransactionResponse,
    OperatorTransactionType,
    TransactionPostfix
} from "@entities/operator.entities";
import { sumMajorUnits } from "@utils/common";
import { Injectable } from "@nestjs/common";

@Injectable()
export class WinHttpHandler extends BaseHttpHandler
    implements HttpHandler<CommitPaymentRequest<IntegrationGameTokenData>, Balance> {

    public async build(req: IntegrationPaymentRequest): Promise<HTTPOperatorRequest> {
        const winRequest: OperatorTransactionRequest = {
            ...this.buildBaseRequest(),
            PrivateToken: req.gameTokenData.privateToken,
            TransactionType: OperatorTransactionType.Return,
            ActionType: OperatorActionType.Standard,
            Amount: sumMajorUnits(req.request.totalWin),
            CurrencyCode: req.gameTokenData.currency,
            ExternalTransactionID: req.request.transactionId.publicId + TransactionPostfix.WIN,
            GameRoundID: req.operatorRoundId
        };

        return this.buildHttpRequest({
            path: "Transaction",
            method: "post",
            payload: winRequest,
            jurisdiction: req.gameTokenData.jurisdiction,
            merchantInfo: req.merchantInfo,
            retryAvailable: true
        } as HttpHandlerRequest);
    }

    public async parse(response: superagent.Response): Promise<Balance> {
        const operatorResponse = this.parseHttpResponse<OperatorTransactionResponse>(response);

        return {
            totalAmount: operatorResponse.Balance.TotalAmount,
            cashAmount: operatorResponse.Balance.CashAmount || 0,
            bonusAmount: operatorResponse.Balance.BonusAmount || 0,
            currency: operatorResponse.CurrencyCode
        };
    }
}
```
