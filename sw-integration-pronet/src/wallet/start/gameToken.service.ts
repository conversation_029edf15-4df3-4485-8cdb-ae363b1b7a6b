import { Injectable } from "@nestjs/common";
import { CreateGameTokenSupport } from "@skywind-group/sw-integration-core";
import { IntegrationGameTokenData, IntegrationInitRequest } from "@entities/pces.entities";
import { logging, measures } from "@skywind-group/sw-utils";
import { ValidationError } from "@errors/pces.errors";
import { sanitizeGameId, formatCurrency, parsePlatformType } from "@utils/pces.utils";

const log = logging.logger("GameTokenService");
const { measure } = measures;

@Injectable()
export class GameTokenService implements CreateGameTokenSupport<IntegrationGameTokenData> {
    
    @measure({ name: "GameTokenService.createGameToken", isAsync: true })
    public async createGameToken(request: IntegrationInitRequest): Promise<IntegrationGameTokenData> {
        this.validateRequest(request);
        
        const gameTokenData: IntegrationGameTokenData = {
            // SW standard fields
            playerCode: request.customer || "",
            currency: formatCurrency(request.currency, request.demo),
            gameCode: sanitizeGameId(request.gameId),
            
            // PCES-specific fields
            customer: request.customer || "",
            token: request.token || "",
            gameId: sanitizeGameId(request.gameId),
            demo: request.demo,
            platform: parsePlatformType(request.platform) as any,
            language: request.lang.toLowerCase(),
            country: request.country || "",
            trader: request.trader,
            tableId: request.tableId,
            lobby: request.lobby
        };
        
        log.info("Created game token for PCES", { 
            customer: gameTokenData.customer,
            gameId: gameTokenData.gameId,
            demo: gameTokenData.demo 
        });
        
        return gameTokenData;
    }
    
    private validateRequest(request: IntegrationInitRequest): void {
        if (!request.gameId || request.gameId.trim() === "") {
            throw new ValidationError("Game ID is required");
        }
        
        if (!request.lang || request.lang.trim() === "") {
            throw new ValidationError("Language is required");
        }
        
        if (!request.trader || request.trader.trim() === "") {
            throw new ValidationError("Trader is required");
        }
        
        if (!request.demo && (!request.customer || !request.token)) {
            throw new ValidationError("Customer and token are required for real money games");
        }
        
        if (!request.demo && (!request.currency || request.currency.trim() === "")) {
            throw new ValidationError("Currency is required for real money games");
        }
    }
}
