import { Injectable } from "@nestjs/common";
import {
    BalanceSupport,
    BonusPaymentSupport,
    CommitPaymentRequest,
    HttpGateway,
    JackpotPaymentSupport,
    SplitPaymentSupport
} from "@skywind-group/sw-integration-core";
import { Balance, Balances, RequireRefundBetError, SWError } from "@skywind-group/sw-wallet-adapter-core";
import { IntegrationGameTokenData, IntegrationPaymentRequest } from "@entities/pces.entities";
import { BetHttpHandler } from "@wallet/payment/bet.http.handler";
import { WinHttpHandler } from "@wallet/payment/win.http.handler";
import { BalanceHttpHandler } from "@wallet/payment/balance.http.handler";
import { DebitCreditHttpHandler } from "@wallet/payment/debitCredit.http.handler";
import { RollbackHttpHandler } from "@wallet/payment/rollback.http.handler";
import { PromoHttpHandler } from "@wallet/payment/promo.http.handler";
import { logging, measures } from "@skywind-group/sw-utils";
import { 
    PCESBetAlreadySettledError, 
    PCESTokenExpiredError, 
    PCESCustomerNotFoundError,
    refundCondition,
    rollbackCondition
} from "@errors/pces.errors";

const log = logging.logger("PaymentService");
const { measure } = measures;

@Injectable()
export class PaymentService implements 
    BalanceSupport, 
    SplitPaymentSupport, 
    JackpotPaymentSupport, 
    BonusPaymentSupport {
    
    constructor(
        private readonly httpGateway: HttpGateway,
        private readonly betHandler: BetHttpHandler,
        private readonly winHandler: WinHttpHandler,
        private readonly balanceHandler: BalanceHttpHandler,
        private readonly debitCreditHandler: DebitCreditHttpHandler,
        private readonly rollbackHandler: RollbackHttpHandler,
        private readonly promoHandler: PromoHttpHandler
    ) {}

    @measure({ name: "PaymentService.getBalance", isAsync: true })
    public async getBalance(req: IntegrationPaymentRequest): Promise<Balance> {
        return await this.httpGateway.request<IntegrationPaymentRequest, Balance>(
            req,
            this.balanceHandler
        );
    }

    @measure({ name: "PaymentService.commitBetPayment", isAsync: true })
    public async commitBetPayment(req: IntegrationPaymentRequest): Promise<Balance> {
        try {
            if (!req.request.bet || req.request.bet === 0) {
                log.info("Fetch balance instead of zero-bet");
                return this.getBalance(req);
            }
            
            // For PCES, we can use either separate debit or combined debit-credit
            // Use debit-credit if we have both bet and win in the same request
            if (req.request.totalWin && req.request.totalWin > 0) {
                log.info("Using debit-credit for combined bet and win");
                return await this.httpGateway.request<IntegrationPaymentRequest, Balance>(
                    req,
                    this.debitCreditHandler
                );
            } else {
                return await this.httpGateway.request<IntegrationPaymentRequest, Balance>(
                    req,
                    this.betHandler
                );
            }
        } catch (err) {
            if (this.isRefundNeeded(req, err)) {
                log.info(err, "Refund PCES bet");
                throw new RequireRefundBetError();
            }

            if (this.isRollbackNeeded(req, err)) {
                log.info(err, "Rollback PCES bet");
                await this.rollbackTransaction(req);
            }

            log.info(err, "Bet payment failed");
            throw err;
        }
    }

    @measure({ name: "PaymentService.commitWinPayment", isAsync: true })
    public async commitWinPayment(req: IntegrationPaymentRequest): Promise<Balance> {
        let balance: Balance;

        if (this.isWinCommittable(req)) {
            try {
                balance = await this.httpGateway.request<IntegrationPaymentRequest, Balance>(
                    req,
                    this.winHandler
                );
            } catch (err) {
                if (this.isBetAlreadySettled(err) && req.request.retry) {
                    log.warn(err, "Bet is already settled. Fetch balance instead");
                    return this.getBalance(req);
                }
                throw err;
            }
        } else {
            log.info("Fetch balance instead of zero-win");
            balance = await this.getBalance(req);
        }

        return balance;
    }

    @measure({ name: "PaymentService.commitJackpotWinPayment", isAsync: true })
    public async commitJackpotWinPayment(req: IntegrationPaymentRequest): Promise<Balance> {
        // For jackpot wins, use the promo handler with JPW type
        const promoRequest = {
            ...req,
            promoType: "JPW",
            promoRef: req.request.transactionId.publicId
        };
        
        return await this.httpGateway.request<IntegrationPaymentRequest, Balance>(
            promoRequest,
            this.promoHandler
        );
    }

    @measure({ name: "PaymentService.commitBonusPayment", isAsync: true })
    public async commitBonusPayment(req: IntegrationPaymentRequest): Promise<Balance> {
        // For bonus payments, determine the promo type based on the bonus type
        const promoType = this.mapBonusToPromoType(req.request.bonusType);
        const promoRequest = {
            ...req,
            promoType,
            promoRef: req.request.transactionId.publicId
        };
        
        return await this.httpGateway.request<IntegrationPaymentRequest, Balance>(
            promoRequest,
            this.promoHandler
        );
    }

    public async getBalances(req: IntegrationPaymentRequest): Promise<Balances> {
        const balance = await this.getBalance(req);
        return {
            totalBalance: balance,
            cashBalance: {
                totalAmount: balance.cashAmount || 0,
                currency: balance.currency
            },
            bonusBalance: {
                totalAmount: balance.bonusAmount || 0,
                currency: balance.currency
            }
        };
    }

    @measure({ name: "PaymentService.rollbackTransaction", isAsync: true })
    public async rollbackTransaction(req: IntegrationPaymentRequest): Promise<Balance> {
        try {
            return await this.httpGateway.request<IntegrationPaymentRequest, Balance>(
                req,
                this.rollbackHandler
            );
        } catch (err) {
            log.error(err, "Failed to rollback transaction");
            // Return current balance if rollback fails
            return this.getBalance(req);
        }
    }

    private isWinCommittable(req: IntegrationPaymentRequest): boolean {
        return req.request.totalWin > 0;
    }

    private isRefundNeeded(req: IntegrationPaymentRequest, err: SWError): boolean {
        return refundCondition(err);
    }

    private isRollbackNeeded(req: IntegrationPaymentRequest, err: SWError): boolean {
        return rollbackCondition(err);
    }

    private isBetAlreadySettled(err: SWError): boolean {
        return err instanceof PCESBetAlreadySettledError;
    }

    private mapBonusToPromoType(bonusType?: string): string {
        switch (bonusType?.toLowerCase()) {
            case "freespin":
            case "freespins":
                return "FSW";
            case "cashback":
                return "CB";
            case "tournament":
                return "TW";
            case "reward":
                return "RW";
            case "rakeback":
                return "RB";
            default:
                return "RW"; // Default to reward
        }
    }
}
