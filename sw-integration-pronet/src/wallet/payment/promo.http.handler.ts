import { CommitPaymentRequest, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPOperatorRequest } from "@skywind-group/sw-integration-core";
import { IntegrationGameTokenData, IntegrationPaymentRequest } from "@entities/pces.entities";
import { BaseHttp<PERSON><PERSON><PERSON>, HttpHandlerRequest } from "@utils/baseHttp.handler";
import * as superagent from "superagent";
import { Balance } from "@skywind-group/sw-wallet-adapter-core";
import {
    PCESPromoRequest,
    PCESPromoResponse
} from "@entities/operator.entities";
import { sumMajorUnits } from "@utils/pces.utils";
import { Injectable } from "@nestjs/common";

@Injectable()
export class PromoHttpHandler extends BaseHttpHandler
    implements HttpHandler<CommitPaymentRequest<IntegrationGameTokenData>, Balance> {
    
    public async build(req: IntegrationPaymentRequest): Promise<HTTPOperatorRequest> {
        const gameTokenData = this.getGameTokenData(req);
        
        this.validateRequiredFields(gameTokenData, ["customer", "token", "gameId", "currency"]);
        this.validateRequiredFields(req.request, ["totalWin", "transactionId", "roundPID"]);
        this.validateRequiredFields(req, ["promoType", "promoRef"]);

        const customer = this.extractCustomerFromToken(gameTokenData);
        const token = this.extractTokenFromGameData(gameTokenData);
        const gameId = this.extractGameId(gameTokenData);
        const currency = this.extractCurrency(gameTokenData);
        
        const promoAmount = this.sanitizeAmount(sumMajorUnits(req.request.totalWin));
        const betId = this.generateBetId(req.request.roundPID);
        const trxId = this.generateTransactionId("promo");

        const promoRequest: PCESPromoRequest = this.buildPCESRequest(customer, token, {
            gameId,
            amount: promoAmount,
            currency,
            betId,
            trxId,
            promo: this.buildPromoInfo(req.promoType, req.promoRef, req.freeSpinData)
        });

        return this.buildHttpRequest({
            endpoint: "promo",
            method: "post",
            payload: promoRequest,
            merchantInfo: req.merchantInfo,
            retryAvailable: true
        } as HttpHandlerRequest);
    }

    public async parse(response: superagent.Response): Promise<Balance> {
        const pcesResponse = this.parseHttpResponse<PCESPromoResponse>(response);
        
        return {
            totalAmount: this.sanitizeAmount(pcesResponse.balance + (pcesResponse.bonusBalance || 0)),
            cashAmount: this.sanitizeAmount(pcesResponse.balance),
            bonusAmount: this.sanitizeAmount(pcesResponse.bonusBalance || 0),
            currency: pcesResponse.currency
        };
    }
}
