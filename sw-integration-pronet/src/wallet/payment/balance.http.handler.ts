import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPOperatorRequest } from "@skywind-group/sw-integration-core";
import { Balance } from "@skywind-group/sw-wallet-adapter-core";
import * as superagent from "superagent";
import { Base<PERSON>ttp<PERSON><PERSON><PERSON>, HttpHandlerRequest } from "@utils/baseHttp.handler";
import { 
    IntegrationGetBalanceRequest,
    IntegrationPaymentRequest
} from "@entities/pces.entities";
import { 
    PCESAuthRequest,
    PCESAuthResponse 
} from "@entities/operator.entities";
import { Injectable } from "@nestjs/common";

@Injectable()
export class BalanceHttpHandler extends BaseHttpHandler 
    implements HttpHandler<IntegrationGetBalanceRequest | IntegrationPaymentRequest, Balance> {
    
    public async build(req: IntegrationGetBalanceRequest | IntegrationPaymentRequest): Promise<HTTPOperatorRequest> {
        const gameTokenData = this.getGameTokenData(req);
        
        this.validateRequiredFields(gameTokenData, ["customer", "token"]);

        const customer = this.extractCustomerFromToken(gameTokenData);
        const token = this.extractTokenFromGameData(gameTokenData);

        const authRequest: PCESAuthRequest = this.buildPCESRequest(customer, token);

        return this.buildHttpRequest({
            endpoint: "auth",
            method: "post",
            payload: authRequest,
            merchantInfo: req.merchantInfo,
            retryAvailable: true
        } as HttpHandlerRequest);
    }

    public async parse(response: superagent.Response): Promise<Balance> {
        const pcesResponse = this.parseHttpResponse<PCESAuthResponse>(response);
        
        return {
            totalAmount: this.sanitizeAmount(pcesResponse.balance + (pcesResponse.bonusBalance || 0)),
            cashAmount: this.sanitizeAmount(pcesResponse.balance),
            bonusAmount: this.sanitizeAmount(pcesResponse.bonusBalance || 0),
            currency: pcesResponse.currency
        };
    }
}
