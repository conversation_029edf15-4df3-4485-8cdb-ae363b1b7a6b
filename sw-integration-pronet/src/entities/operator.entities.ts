import { IsDefined, IsNotEmpty, IsOptional } from "class-validator";

// Base PCES request/response interfaces
export interface PCESBaseRequest {
    customer: string;
    token: string;
    hash?: string;
}

export interface PCESBaseResponse {
    code: number;
    status: string;
    currency?: string;
    balance?: number;
    bonusBalance?: number;
    trxId?: number;
    creditTrxId?: number;
    traderId?: number;
}

export interface PCESError {
    code: number;
    status: string;
}

// Authentication
export interface PCESAuthRequest extends PCESBaseRequest {}

export interface PCESAuthResponse extends PCESBaseResponse {
    balance: number;
    bonusBalance: number;
    currency: string;
    traderId: number;
}

// Transaction operations
export interface PCESDebitRequest extends PCESBaseRequest {
    gameId: string;
    amount: number;
    currency: string;
    betId: string;
    trxId: string;
    tip?: boolean;
}

export interface PCESDebitResponse extends PCESBaseResponse {
    balance: number;
    bonusBalance: number;
    currency: string;
    trxId: number;
}

export interface PCESCreditRequest extends PCESBaseRequest {
    gameId: string;
    amount: number;
    currency: string;
    betId: string;
    trxId: string;
    freespin?: PCESFreespinInfo;
}

export interface PCESCreditResponse extends PCESBaseResponse {
    balance: number;
    bonusBalance: number;
    currency: string;
    trxId?: number;
}

export interface PCESDebitCreditRequest extends PCESBaseRequest {
    gameId: string;
    amount: number;
    creditAmount: number;
    currency: string;
    betId: string;
    trxId: string;
    creditTrxId: string;
    tip?: boolean;
}

export interface PCESDebitCreditResponse extends PCESBaseResponse {
    balance: number;
    bonusBalance: number;
    currency: string;
    trxId: number;
    creditTrxId: number;
}

export interface PCESRollbackRequest extends PCESBaseRequest {
    gameId: string;
    trxId: string;
}

export interface PCESRollbackResponse extends PCESBaseResponse {
    balance: number;
    bonusBalance: number;
    currency: string;
    trxId: number;
}

// Promo operations
export interface PCESPromoRequest extends PCESBaseRequest {
    gameId: string;
    amount: number;
    currency: string;
    betId: string;
    trxId: string;
    promo: {
        promoType: string;
        promoRef: string;
        freeSpinData?: {
            remainingRounds?: number;
            totalWinnings?: number;
            requested?: boolean;
        };
    };
}

export interface PCESPromoResponse extends PCESBaseResponse {
    balance: number;
    bonusBalance: number;
    currency: string;
    trxId: number;
}

// Freespin info
export interface PCESFreespinInfo {
    freespinRef: string;
    requested?: boolean;
    remainingRounds?: number;
    totalWinnings?: number;
}

// History
export class PCESHistoryRequest {
    @IsDefined()
    @IsNotEmpty()
    customer: string;

    @IsDefined()
    @IsNotEmpty()
    roundId: string;

    @IsDefined()
    @IsNotEmpty()
    gameId: string;

    @IsDefined()
    @IsNotEmpty()
    lang: string;

    @IsOptional()
    @IsNotEmpty()
    trader?: string;
}

// Error codes specific to PCES
export const pcesErrorCodes = {
    SUCCESS: 0,
    UNKNOWN_ERROR: -1,
    UNAUTHORIZED_REQUEST: -2,
    NOT_INTEGRATED: -3,
    TOKEN_CUSTOMER_MISMATCH: -4,
    UNSUPPORTED_API_VERSION: -5,
    INTERNAL_CACHE_ERROR: -6,
    PROMOTION_TYPE_NOT_SUPPORTED: -7,
    BET_RECORD_NOT_FOUND: -20120,
    BET_ALREADY_WON: -20112,
    AUTHENTICATION_FAILED: -20101,
    GAME_NOT_FOUND: -20130,
    BET_LIMIT_REACHED: -20201,
    LOSS_LIMIT_REACHED: -20202,
    SESSION_LIMIT_REACHED: -20203,
    PROFIT_LIMIT_REACHED: -20204,
    INVALID_CASINO_VENDOR: -20301,
    ALL_BET_ARE_OFF: -20302,
    INVALID_GAME: -20303,
    CUSTOMER_NOT_FOUND: -20304,
    INVALID_CURRENCY: -20305,
    INSUFFICIENT_FUNDS: -20306,
    PLAYER_SUSPENDED: -20307,
    REQUIRED_FIELD_MISSING: -20308,
    DATA_OUT_OF_RANGE: -20309,
    BET_ALREADY_SETTLED: -20310,
    TOKEN_NOT_FOUND: -20316,
    TOKEN_TIMEOUT: -20311,
    TOKEN_INVALID: -20312,
    TRANSACTION_NOT_FOUND: -20313,
    NEGATIVE_DEPOSIT: -20314,
    NEGATIVE_WITHDRAWAL: -20315
};

// Status messages
export const pcesStatusMessages = {
    [pcesErrorCodes.SUCCESS]: "SUCCESS",
    [pcesErrorCodes.UNKNOWN_ERROR]: "UNKNOWN_ERROR",
    [pcesErrorCodes.UNAUTHORIZED_REQUEST]: "UNAUTHORIZED_REQUEST",
    [pcesErrorCodes.NOT_INTEGRATED]: "NOT_INTEGRATED",
    [pcesErrorCodes.TOKEN_CUSTOMER_MISMATCH]: "TOKEN_CUSTOMER_MISMATCH",
    [pcesErrorCodes.UNSUPPORTED_API_VERSION]: "UNSUPPORTED_API_VERSION",
    [pcesErrorCodes.INTERNAL_CACHE_ERROR]: "INTERNAL_CACHE_ERROR",
    [pcesErrorCodes.PROMOTION_TYPE_NOT_SUPPORTED]: "PROMOTION_TYPE_NOT_SUPPORTED",
    [pcesErrorCodes.BET_RECORD_NOT_FOUND]: "BET_RECORD_NOT_FOUND",
    [pcesErrorCodes.BET_ALREADY_WON]: "BET_ALREADY_WON",
    [pcesErrorCodes.AUTHENTICATION_FAILED]: "AUTHENTICATION_FAILED",
    [pcesErrorCodes.GAME_NOT_FOUND]: "GAME_NOT_FOUND",
    [pcesErrorCodes.BET_LIMIT_REACHED]: "BET_LIMIT_REACHED",
    [pcesErrorCodes.LOSS_LIMIT_REACHED]: "LOSS_LIMIT_REACHED",
    [pcesErrorCodes.SESSION_LIMIT_REACHED]: "SESSION_LIMIT_REACHED",
    [pcesErrorCodes.PROFIT_LIMIT_REACHED]: "PROFIT_LIMIT_REACHED",
    [pcesErrorCodes.INVALID_CASINO_VENDOR]: "INVALID_CASINO_VENDOR",
    [pcesErrorCodes.ALL_BET_ARE_OFF]: "ALL_BET_ARE_OFF",
    [pcesErrorCodes.INVALID_GAME]: "INVALID_GAME",
    [pcesErrorCodes.CUSTOMER_NOT_FOUND]: "CUSTOMER_NOT_FOUND",
    [pcesErrorCodes.INVALID_CURRENCY]: "INVALID_CURRENCY",
    [pcesErrorCodes.INSUFFICIENT_FUNDS]: "INSUFFICIENT_FUNDS",
    [pcesErrorCodes.PLAYER_SUSPENDED]: "PLAYER_SUSPENDED",
    [pcesErrorCodes.REQUIRED_FIELD_MISSING]: "REQUIRED_FIELD_MISSING",
    [pcesErrorCodes.DATA_OUT_OF_RANGE]: "DATA_OUT_OF_RANGE",
    [pcesErrorCodes.BET_ALREADY_SETTLED]: "BET_ALREADY_SETTLED",
    [pcesErrorCodes.TOKEN_NOT_FOUND]: "TOKEN_NOT_FOUND",
    [pcesErrorCodes.TOKEN_TIMEOUT]: "TOKEN_TIMEOUT",
    [pcesErrorCodes.TOKEN_INVALID]: "TOKEN_INVALID",
    [pcesErrorCodes.TRANSACTION_NOT_FOUND]: "TRANSACTION_NOT_FOUND",
    [pcesErrorCodes.NEGATIVE_DEPOSIT]: "NEGATIVE_DEPOSIT",
    [pcesErrorCodes.NEGATIVE_WITHDRAWAL]: "NEGATIVE_WITHDRAWAL"
};
