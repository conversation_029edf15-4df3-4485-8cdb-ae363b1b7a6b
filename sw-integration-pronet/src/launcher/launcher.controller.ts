import { Controller, Get, Query, UseFilters, Redirect, HttpStatus } from "@nestjs/common";
import { ValidationPipe } from "@nestjs/common";
import { LauncherService } from "./launcher.service";
import { IntegrationGameLaunchRequest } from "@entities/pces.entities";
import { BaseErrorsFilter } from "@skywind-group/sw-integration-core";
import { ClientIp } from "@utils/clientIp.decorator";

@Controller("game")
export class LauncherController {
    constructor(private launcherService: LauncherService) {}

    @Get("/url")
    @UseFilters(BaseErrorsFilter)
    @Redirect(undefined, HttpStatus.FOUND)
    async getGameUrl(
        @Query(new ValidationPipe()) data: IntegrationGameLaunchRequest,
        @ClientIp() ip: string
    ): Promise<{ url: string }> {
        const url = await this.launcherService.getGameUrl(data, ip);
        return { url };
    }

    @Get("/url/noredirect")
    @UseFilters(BaseErrorsFilter)
    async getGameUrlWithoutRedirection(
        @Query(new ValidationPipe()) data: IntegrationGameLaunchRequest,
        @ClientIp() ip: string
    ): Promise<{ url: string }> {
        const url = await this.launcherService.getGameUrl(data, ip);
        return { url };
    }

    @Get("/pces/url")
    @UseFilters(BaseErrorsFilter)
    @Redirect(undefined, HttpStatus.FOUND)
    async getPCESGameUrl(
        @Query(new ValidationPipe()) data: IntegrationGameLaunchRequest
    ): Promise<{ url: string }> {
        const url = this.launcherService.buildPCESGameUrl(data);
        return { url };
    }

    @Get("/pces/url/noredirect")
    @UseFilters(BaseErrorsFilter)
    async getPCESGameUrlWithoutRedirection(
        @Query(new ValidationPipe()) data: IntegrationGameLaunchRequest
    ): Promise<{ url: string }> {
        const url = this.launcherService.buildPCESGameUrl(data);
        return { url };
    }
}
