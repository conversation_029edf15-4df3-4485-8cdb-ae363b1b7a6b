import { createParamDecorator, ExecutionContext } from "@nestjs/common";

export const ClientIp = createParamDecorator(
    (data: unknown, ctx: ExecutionContext): string => {
        const request = ctx.switchToHttp().getRequest();
        return request.ip || 
               request.connection.remoteAddress || 
               request.socket.remoteAddress ||
               (request.connection.socket ? request.connection.socket.remoteAddress : null) ||
               "127.0.0.1";
    }
);
