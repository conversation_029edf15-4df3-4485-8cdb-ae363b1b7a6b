import * as CryptoJS from "crypto-js";
import config from "@config";

export function generateNumber(length: number): number {
    const min = Math.pow(10, length - 1);
    const max = Math.pow(10, length) - 1;
    return Math.floor(Math.random() * (max - min + 1)) + min;
}

export function generateUniqueId(): string {
    return `${Date.now()}_${generateNumber(6)}`;
}

export function sumMajorUnits(...amounts: number[]): number {
    const total = amounts.reduce((sum, amount) => sum + (amount || 0), 0);
    return Math.round(total * config.currencyUnitMultiplier) / config.currencyUnitMultiplier;
}

export function convertToMinorUnits(amount: number): number {
    return Math.round(amount * config.currencyUnitMultiplier);
}

export function convertFromMinorUnits(amount: number): number {
    return amount / config.currencyUnitMultiplier;
}

/**
 * Calculate PCES hash using HMAC-SHA256
 * Hash should be calculated by marshaling the request body json, 
 * concatenating genericSecretKey and applying SHA256 over the marshaled string.
 */
export function calculatePCESHash(requestBody: any): string {
    const jsonString = JSON.stringify(requestBody);
    const toHash = jsonString + config.pces.genericSecretKey;
    return CryptoJS.SHA256(toHash).toString();
}

/**
 * Build PCES API URL
 */
export function buildPCESApiUrl(endpoint: string): string {
    const baseUrl = config.http.operatorUrl;
    const vendorCode = config.pces.vendorCode;
    const apiVersion = config.pces.apiVersion;
    
    return `${baseUrl}/casino-engine/generic/${vendorCode}/${apiVersion}/${endpoint}`;
}

/**
 * Build PCES headers
 */
export function buildPCESHeaders(hash: string): { [key: string]: string } {
    return {
        "Content-Type": "application/json",
        "Generic-Id": config.pces.genericId,
        "Hash": hash
    };
}

/**
 * Create game URL with query parameters
 */
export function buildGameUrl(baseUrl: string, params: { [key: string]: any }): string {
    const queryParams = new URLSearchParams();
    
    Object.keys(params).forEach(key => {
        if (params[key] !== null && params[key] !== undefined) {
            queryParams.append(key, params[key].toString());
        }
    });
    
    return `${baseUrl}?${queryParams.toString()}`;
}

/**
 * Validate PCES token expiration
 */
export function isTokenExpired(tokenCreatedAt: Date): boolean {
    const now = new Date();
    const expirationTime = new Date(tokenCreatedAt.getTime() + (config.pces.tokenExpirationMinutes * 60 * 1000));
    return now > expirationTime;
}

/**
 * Format currency for PCES API
 */
export function formatCurrency(currency: string, demo: boolean): string {
    return demo ? "FUN" : currency.toUpperCase();
}

/**
 * Parse platform type
 */
export function parsePlatformType(platform: string): string {
    switch (platform.toLowerCase()) {
        case "desktop":
        case "d":
            return "d";
        case "mobile":
        case "m":
            return "m";
        default:
            return "d";
    }
}

/**
 * Generate bet ID for PCES
 */
export function generateBetId(roundId: string): string {
    return `bet_${roundId}_${Date.now()}`;
}

/**
 * Generate transaction ID for PCES
 */
export function generateTransactionId(type: "bet" | "win" | "rollback" = "bet"): string {
    return `${type}_${Date.now()}_${generateNumber(6)}`;
}

/**
 * Validate PCES response
 */
export function isPCESSuccess(response: any): boolean {
    return response && response.code === 0 && response.status === "SUCCESS";
}

/**
 * Extract error message from PCES response
 */
export function extractPCESError(response: any): { code: number; message: string } {
    if (response && response.code !== 0) {
        return {
            code: response.code,
            message: response.status || "Unknown error"
        };
    }
    return {
        code: -1,
        message: "Unknown error"
    };
}

/**
 * Build round details URL
 */
export function buildRoundDetailsUrl(baseUrl: string, params: {
    customer: string;
    roundId: string;
    gameId: string;
    lang: string;
    trader?: string;
}): string {
    return buildGameUrl(baseUrl, params);
}

/**
 * Sanitize game ID for PCES
 */
export function sanitizeGameId(gameId: string): string {
    return gameId.trim().substring(0, 50);
}

/**
 * Sanitize table ID for PCES
 */
export function sanitizeTableId(tableId?: string): string | null {
    if (!tableId || tableId.trim() === "") {
        return null;
    }
    return tableId.trim().substring(0, 50);
}

/**
 * Format date for PCES API
 */
export function formatDateForPCES(date: Date): string {
    return date.toISOString();
}

/**
 * Create demo game parameters
 */
export function createDemoGameParams(gameId: string, lang: string, platform: string, trader: string, lobby?: string, tableId?: string): any {
    return {
        currency: "FUN",
        demo: true,
        gameId: sanitizeGameId(gameId),
        lang: lang.toLowerCase(),
        lobby: lobby,
        platform: parsePlatformType(platform),
        tableId: sanitizeTableId(tableId),
        trader: trader
    };
}

/**
 * Create real game parameters
 */
export function createRealGameParams(
    currency: string,
    customer: string,
    gameId: string,
    lang: string,
    platform: string,
    token: string,
    trader: string,
    country?: string,
    lobby?: string,
    tableId?: string
): any {
    return {
        currency: currency.toUpperCase(),
        customer: customer,
        demo: false,
        gameId: sanitizeGameId(gameId),
        lang: lang.toLowerCase(),
        lobby: lobby,
        platform: parsePlatformType(platform),
        tableId: sanitizeTableId(tableId),
        token: token,
        trader: trader,
        country: country
    };
}
