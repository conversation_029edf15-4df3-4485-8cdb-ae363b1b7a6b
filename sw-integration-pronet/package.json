{"name": "sw-integration-pces", "version": "1.0.0", "description": "SW Integration for PCES Generic Vendor API v5", "author": "SW Integration Team", "private": true, "license": "UNLICENSED", "scripts": {"clean": "rm -rf ./lib", "build": "npm run version && nest build", "format": "prettier --write \"**/*.ts\"", "start:launcher": "nest start launcher", "start:launcher:dev": "MEASURES_PROVIDER=prometheus nest start launcher --watch", "start:operator": "nest start operator", "start:operator:dev": "MEASURES_PROVIDER=prometheus nest start operator --watch", "start:wallet": "nest start wallet", "start:wallet:dev": "MEASURES_PROVIDER=prometheus nest start wallet --watch", "start:mock": "nest start mock", "test": "mocha -r ts-node/register --exit src/**/*.spec.ts", "test:watch": "npm run test -- --watch", "version": "node -e \"require('fs').writeFileSync('lib/version', require('./package.json').version)\""}, "dependencies": {"@nestjs/common": "^7.4.2", "@nestjs/core": "^7.4.2", "@nestjs/platform-fastify": "^7.0.9", "@nestjs/schedule": "^1.1.0", "@nestjs/typeorm": "^7.1.5", "@skywind-group/sw-integration-core": "^1.0.82", "@skywind-group/sw-utils": "^1.0.4", "@skywind-group/sw-wallet-adapter-core": "^0.6.194", "class-transformer": "^0.3.1", "class-validator": "^0.12.2", "crypto-js": "^4.1.1", "fastify": "^2.14.1", "module-alias": "^2.2.2", "pg": "^8.3.3", "typeorm": "^0.2.45"}, "devDependencies": {"@nestjs/testing": "^7.4.2", "@types/chai": "^4.2.12", "@types/crypto-js": "^4.1.1", "@types/mocha": "^7.0.2", "@types/node": "^7.10.9", "chai": "^4.2.0", "mocha": "^7.1.2", "mocha-typescript": "^1.1.17", "prettier": "^2.0.5", "ts-node": "^8.10.2", "typescript": "^3.9.7"}, "_moduleAliases": {"@entities": "src/entities", "@errors": "src/errors", "@utils": "src/utils", "@config": "src/config", "@wallet": "src/wallet", "@payment": "src/wallet/payment", "@launcher": "src/launcher", "@operator": "src/operator", "@mock": "src/mock"}}