# SW Integration for PCES Generic Vendor API v5

This project implements a complete integration with the PCES Generic Vendor API v5 following the SW Integration architecture patterns.

## Overview

The PCES (ProNet Casino Engine System) integration supports:

- **Authentication** via customer tokens
- **Balance Management** (regular + bonus balance)
- **Transaction Operations** (debit, credit, rollback, debit-credit)
- **Promotional Wins** (freespins, jackpots, tournaments, etc.)
- **Round Details/History**
- **Hash-based Security** (HMAC-SHA256)

## Architecture

The system follows a microservices architecture with three main services:

- **Wallet Service** (Port 3000) - Handles payments, balance management, and financial transactions
- **Launcher Service** (Port 3001) - Manages game URL generation and game launching
- **Operator Service** (Port 3002) - Handles operator-specific integrations and round history

## API Endpoints

### PCES API Endpoints
- `/auth` - Get balance and authenticate
- `/debit` - Place bet (debit player balance)
- `/credit` - Settle bet (credit player balance)
- `/debit-credit` - Combined bet and settlement
- `/rollback` - Reverse transaction
- `/promo` - Handle promotional wins

### Integration Endpoints

#### Launcher Service
- `GET /game/url` - Get game URL with redirect
- `GET /game/url/noredirect` - Get game URL without redirect
- `GET /game/pces/url` - Get direct PCES game URL with redirect
- `GET /game/pces/url/noredirect` - Get direct PCES game URL without redirect

#### Operator Service
- `GET /history/image` - Get game history image URL
- `GET /history/details` - Get game history details

## Configuration

### Environment Variables

Copy `.env.example` to `.env` and configure:

```bash
# PCES API Configuration
PCES_BASE_ENGINE_URL=http://casinoengine.test.pronetgaming.com
PCES_VENDOR_CODE=znidi_gaming
PCES_GENERIC_ID=46b0da0cd81423dcdac17d2070b4af16
PCES_GENERIC_SECRET_KEY=86b04d46bb0e81a1131c6e6acd2b7e75

# Server Ports
SERVER_WALLET_PORT=3000
SERVER_LAUNCHER_PORT=3001
SERVER_OPERATOR_PORT=3002

# Database
PGDATABASE=management
PGUSER=postgres
PGPASSWORD=password
PGHOST=localhost
PGPORT=5432
```

## Installation

```bash
# Install dependencies
npm install

# Build the project
npm run build

# Start services
npm run start:wallet      # Wallet service
npm run start:launcher    # Launcher service
npm run start:operator    # Operator service
```

## Development

```bash
# Start in development mode with hot reload
npm run start:wallet:dev
npm run start:launcher:dev
npm run start:operator:dev
```

## Game Launch Examples

### Demo Game
```
GET /game/url?gameId=slot001&demo=true&lang=en&platform=d&trader=demo_trader
```

### Real Money Game
```
GET /game/url?gameId=slot001&demo=false&currency=USD&customer=player123&token=abc123&lang=en&platform=d&trader=real_trader
```

### Direct PCES Game URL
```
GET /game/pces/url?gameId=slot001&demo=true&lang=en&platform=d&trader=demo_trader
```

## PCES Integration Features

### Security
- HMAC-SHA256 hash validation for all requests
- Generic-Id header authentication
- Token-based player authentication

### Transaction Types
- **Debit**: Place bets and deduct from player balance
- **Credit**: Award winnings to player balance
- **Debit-Credit**: Combined bet and win in single transaction
- **Rollback**: Reverse previous transactions
- **Promo**: Handle promotional wins (freespins, jackpots, etc.)

### Promotional Support
- **FSW**: Freespin wins
- **JPW**: Jackpot wins
- **CB**: Cashback
- **TW**: Tournament wins
- **RW**: Rewards
- **REW**: Red envelope wins
- **CDW**: Cash drop wins
- **RB**: Rakeback

### Error Handling
Comprehensive error mapping for PCES error codes:
- Authentication failures
- Insufficient funds
- Token expiration
- Game not found
- Player limits
- Transaction conflicts

## Testing

```bash
# Run tests
npm test

# Run tests in watch mode
npm run test:watch
```

## API Documentation

### Game Launch Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| gameId | string | Yes | Game identifier |
| demo | boolean | Yes | Demo mode flag |
| currency | string | No* | Currency code (required for real money) |
| customer | string | No* | Customer identifier (required for real money) |
| token | string | No* | Authentication token (required for real money) |
| lang | string | Yes | Language code (e.g., 'en', 'es') |
| platform | string | Yes | Platform type ('d' for desktop, 'm' for mobile) |
| trader | string | Yes | Trader identifier |
| tableId | string | No | Table identifier for live games |
| lobby | string | No | Lobby URL |
| country | string | No | Country code |

*Required for real money games (demo=false)

### Response Formats

#### Balance Response
```json
{
  "totalAmount": 100.50,
  "cashAmount": 80.25,
  "bonusAmount": 20.25,
  "currency": "USD"
}
```

#### Game URL Response
```json
{
  "url": "http://casinoengine.test.pronetgaming.com/casino-engine/game?gameId=slot001&demo=true&lang=en&platform=d&trader=demo_trader"
}
```

## Monitoring

The integration includes built-in monitoring and metrics:
- Performance measurements
- Error tracking
- Request/response logging
- Health check endpoints

## Security Considerations

1. **Hash Validation**: All PCES API requests include HMAC-SHA256 hash
2. **Token Management**: Player tokens have configurable expiration
3. **SSL Support**: Optional SSL/TLS configuration
4. **Input Validation**: Comprehensive request validation
5. **Error Sanitization**: Secure error message handling

## Troubleshooting

### Common Issues

1. **Hash Validation Errors**: Verify `PCES_GENERIC_SECRET_KEY` configuration
2. **Token Expiration**: Check `PCES_TOKEN_EXPIRATION_MINUTES` setting
3. **Connection Issues**: Verify `PCES_BASE_ENGINE_URL` and network connectivity
4. **Game Not Found**: Ensure game ID is valid and available

### Logs

Enable detailed logging:
```bash
LOG_LEVEL=debug npm run start:wallet:dev
```

## License

This project is licensed under the same terms as the SW Integration framework.
